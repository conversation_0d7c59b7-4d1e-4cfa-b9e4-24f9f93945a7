import { Request, Response } from 'express';
import { ProgressModel } from '../models/Progress';
import { CourseModel } from '../models/Course';
import { UserRole } from '../../../shared/types';
import { createApiResponse, calculatePace } from '../../../shared/utils/helpers';
import { HTTP_STATUS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import axios from 'axios';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class ProgressController {
  async getStudentProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const progressRecords = await ProgressModel.find({ studentId });

      // Get subscription info from payment service
      const subscriptionInfo = await this.getSubscriptionInfo(studentId);

      const progressWithPacing = await Promise.all(
        progressRecords.map(async (progress) => {
          const course = await CourseModel.findById(progress.courseId);
          if (!course) return null;

          const subscription = subscriptionInfo.find((sub: any) => sub.courseId === progress.courseId);
          let paceStatus = null;

          if (subscription && subscription.status === 'active') {
            paceStatus = calculatePace(
              new Date(subscription.startDate),
              course.modules.length,
              progress.completedModules.length
            );
          }

          return {
            ...progress.toJSON(),
            course: {
              title: course.title,
              category: course.category,
              totalModules: course.modules.length
            },
            subscription,
            paceStatus
          };
        })
      );

      const validProgress = progressWithPacing.filter(p => p !== null);

      res.json(createApiResponse(true, 'Student progress retrieved successfully', validProgress));

    } catch (error) {
      logger.error('Get student progress error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve student progress', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getCourseProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const progress = await ProgressModel.findOne({ studentId, courseId });
      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Course not found')
        );
      }

      if (!progress) {
        // Create initial progress
        const newProgress = new ProgressModel({
          studentId,
          courseId,
          completedModules: [],
          completedLessons: [],
          completedAssignments: [],
          currentModule: 0,
          currentLesson: 0,
          completionPercentage: 0
        });

        await newProgress.save();

        return res.json(createApiResponse(true, 'Progress initialized', {
          progress: newProgress,
          course: {
            title: course.title,
            totalModules: course.modules.length,
            totalLessons: course.modules.reduce((total, module) => total + module.lessons.length, 0)
          }
        }));
      }

      // Get subscription info for pace calculation
      const subscriptionInfo = await this.getSubscriptionInfo(studentId);
      const subscription = subscriptionInfo.find((sub: any) => sub.courseId === courseId);

      let paceStatus = null;
      if (subscription && subscription.status === 'active') {
        paceStatus = calculatePace(
          new Date(subscription.startDate),
          course.modules.length,
          progress.completedModules.length
        );
      }

      res.json(createApiResponse(true, 'Course progress retrieved successfully', {
        progress,
        course: {
          title: course.title,
          totalModules: course.modules.length,
          totalLessons: course.modules.reduce((total, module) => total + module.lessons.length, 0)
        },
        subscription,
        paceStatus
      }));

    } catch (error) {
      logger.error('Get course progress error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve course progress', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  private async getSubscriptionInfo(studentId: string): Promise<any[]> {
    try {
      const paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://payment-service:3003';
      
      const response = await axios.get(`${paymentServiceUrl}/api/subscriptions/student/${studentId}`, {
        timeout: 5000
      });

      return response.data.data || [];

    } catch (error) {
      logger.error('Failed to get subscription info:', error);
      return [];
    }
  }
}