import mongoose, { Document, Schema } from 'mongoose';
import { Subscription as ISubscription, SubscriptionStatus, Progress } from '../../../shared/types';

export interface SubscriptionDocument extends Omit<ISubscription, '_id'>, Document {}

const progressSchema = new Schema({
  completedModules: [{ type: String }],
  completedLessons: [{ type: String }],
  completedAssignments: [{ type: String }],
  currentModule: { type: Number, default: 0 },
  currentLesson: { type: Number, default: 0 },
  completionPercentage: { type: Number, default: 0 }
}, { _id: false });

const subscriptionSchema = new Schema<SubscriptionDocument>({
  studentId: { type: String, required: true, index: true },
  courseId: { type: String, required: true, index: true },
  status: { type: String, enum: Object.values(SubscriptionStatus), default: SubscriptionStatus.ACTIVE },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  failureCount: { type: Number, default: 0 },
  maxFailures: { type: Number, default: 3 },
  progress: { type: progressSchema, default: () => ({}) },
  paymentId: { type: String, required: true }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret._id = ret._id.toString();
      return ret;
    }
  }
});

// Compound indexes
subscriptionSchema.index({ studentId: 1, courseId: 1 }, { unique: true });
subscriptionSchema.index({ status: 1, endDate: 1 });

export const SubscriptionModel = mongoose.model<SubscriptionDocument>('Subscription', subscriptionSchema);