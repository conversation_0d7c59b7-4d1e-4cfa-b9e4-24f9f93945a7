import { Request, Response } from 'express';
import { SubscriptionModel } from '../models/Subscription';
import { PaymentModel } from '../models/Payment';
import { UserRole, SubscriptionStatus, PaymentStatus } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS, COURSE_CONSTANTS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class SubscriptionController {
  async createSubscription(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, paymentId, duration = COURSE_CONSTANTS.DEFAULT_DURATION } = req.body;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      // Verify payment exists and is paid
      const payment = await PaymentModel.findById(paymentId);

      if (!payment || payment.status !== PaymentStatus.PAID) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createApiResponse(false, 'Valid paid payment required')
        );
      }

      if (payment.studentId !== studentId || payment.courseId !== courseId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createApiResponse(false, 'Payment does not match student or course')
        );
      }

      // Check if subscription already exists
      const existingSubscription = await SubscriptionModel.findOne({ studentId, courseId });

      if (existingSubscription) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          createApiResponse(false, 'Subscription already exists for this course')
        );
      }

      // Create subscription
      const startDate = new Date();
      const endDate = new Date(startDate.getTime() + (duration * 24 * 60 * 60 * 1000));

      const subscription = new SubscriptionModel({
        studentId,
        courseId,
        status: SubscriptionStatus.ACTIVE,
        startDate,
        endDate,
        failureCount: 0,
        maxFailures: COURSE_CONSTANTS.MAX_FAILURES,
        paymentId,
        progress: {
          completedModules: [],
          completedLessons: [],
          completedAssignments: [],
          currentModule: 0,
          currentLesson: 0,
          completionPercentage: 0
        }
      });

      await subscription.save();

      logger.info(`Subscription created for student ${studentId}: Course ${courseId}`);

      res.status(HTTP_STATUS.CREATED).json(
        createApiResponse(true, 'Subscription created successfully', subscription)
      );

    } catch (error) {
      logger.error('Create subscription error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to create subscription', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getStudentSubscriptions(req: AuthenticatedRequest, res: Response) {
    try {
      const { studentId } = req.params;
      const requestingUserId = req.user?.userId;

      // Check permissions
      if (req.user?.role !== UserRole.ADMIN && studentId !== requestingUserId) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Access denied')
        );
      }

      const subscriptions = await SubscriptionModel.find({ studentId }).sort({ createdAt: -1 });

      res.json(createApiResponse(true, 'Student subscriptions retrieved successfully', subscriptions));

    } catch (error) {
      logger.error('Get student subscriptions error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve student subscriptions', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async checkSubscriptionAccess(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const subscription = await SubscriptionModel.findOne({ studentId, courseId });

      if (!subscription) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'No subscription found for this course')
        );
      }

      const now = new Date();
      const hasAccess = subscription.status === SubscriptionStatus.ACTIVE && subscription.endDate > now;

      res.json(createApiResponse(true, 'Subscription access checked', {
        hasAccess,
        subscription: {
          status: subscription.status,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          failureCount: subscription.failureCount,
          maxFailures: subscription.maxFailures
        }
      }));

    } catch (error) {
      logger.error('Check subscription access error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to check subscription access', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async handleAssignmentFailure(req: AuthenticatedRequest, res: Response) {
    try {
      const { studentId, courseId } = req.body;

      const subscription = await SubscriptionModel.findOne({ studentId, courseId });

      if (!subscription) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Subscription not found')
        );
      }

      if (subscription.status !== SubscriptionStatus.ACTIVE) {
        return res.json(createApiResponse(true, 'Subscription already inactive', subscription));
      }

      // Increment failure count
      subscription.failureCount += 1;

      // Check if max failures reached
      if (subscription.failureCount >= subscription.maxFailures) {
        subscription.status = SubscriptionStatus.TERMINATED;
        logger.info(`Subscription terminated due to failures: Student ${studentId}, Course ${courseId}`);
      }

      await subscription.save();

      res.json(createApiResponse(true, 'Assignment failure processed', {
        failureCount: subscription.failureCount,
        maxFailures: subscription.maxFailures,
        status: subscription.status,
        terminated: subscription.status === SubscriptionStatus.TERMINATED
      }));

    } catch (error) {
      logger.error('Handle assignment failure error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to handle assignment failure', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getTerminatedStudents(req: AuthenticatedRequest, res: Response) {
    try {
      // Only tutors and admins can access this
      if (req.user?.role !== UserRole.TUTOR && req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Access denied')
        );
      }

      const { courseId } = req.query;
      const query: any = { status: SubscriptionStatus.TERMINATED };

      if (courseId) {
        query.courseId = courseId;
      }

      const terminatedSubscriptions = await SubscriptionModel.find(query).sort({ updatedAt: -1 });

      res.json(createApiResponse(true, 'Terminated students retrieved successfully', terminatedSubscriptions));

    } catch (error) {
      logger.error('Get terminated students error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve terminated students', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }
}