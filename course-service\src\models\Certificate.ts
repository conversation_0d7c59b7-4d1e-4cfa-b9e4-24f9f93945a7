import mongoose, { Document, Schema } from 'mongoose';
import { Certificate as ICertificate } from '../../../shared/types';

export interface CertificateDocument extends Omit<ICertificate, '_id'>, Document {}

const certificateSchema = new Schema<CertificateDocument>({
  studentId: { type: String, required: true, index: true },
  courseId: { type: String, required: true, index: true },
  verificationCode: { type: String, required: true, unique: true },
  issuedAt: { type: Date, default: Date.now },
  downloadUrl: { type: String, required: true }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret._id = ret._id.toString();
      return ret;
    }
  }
});

// Compound index for uniqueness
certificateSchema.index({ studentId: 1, courseId: 1 }, { unique: true });
certificateSchema.index({ verificationCode: 1 });

export const CertificateModel = mongoose.model<CertificateDocument>('Certificate', certificateSchema);