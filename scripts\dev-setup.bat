@echo off
REM Development setup script untuk Windows
REM Jalankan dengan: scripts\dev-setup.bat [command]

setlocal enabledelayedexpansion

set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

if "%COMMAND%"=="start" goto :start
if "%COMMAND%"=="stop" goto :stop
if "%COMMAND%"=="restart" goto :restart
if "%COMMAND%"=="status" goto :status
if "%COMMAND%"=="db-only" goto :db-only
if "%COMMAND%"=="test-data" goto :test-data
goto :help

:start
echo ℹ️  Starting databases and services...
call :start-databases
call :install-deps
call :start-services
call :setup-test-data
call :show-status
goto :end

:stop
echo ℹ️  Stopping all services...
taskkill /f /im node.exe >nul 2>&1
docker-compose down
echo ✅ All services stopped
goto :end

:restart
echo ℹ️  Restarting services...
taskkill /f /im node.exe >nul 2>&1
call :start-services
goto :end

:status
call :show-status
goto :end

:db-only
echo ℹ️  Starting only databases...
call :start-databases
echo ✅ Only databases started. Use 'npm run dev' in each service directory.
goto :end

:test-data
echo ℹ️  Setting up test data...
call :setup-test-data
goto :end

:start-databases
echo ℹ️  Starting MongoDB databases...
docker-compose up -d mongo-auth mongo-course mongo-payment mongo-analytics
if errorlevel 1 (
    echo ❌ Failed to start databases
    exit /b 1
)
echo ✅ Databases started successfully
echo ℹ️  Waiting for databases to be ready...
timeout /t 10 /nobreak >nul
goto :eof

:install-deps
echo ℹ️  Installing dependencies...
if not exist "auth-service\node_modules" (
    echo Installing auth-service dependencies...
    cd auth-service && npm install && cd ..
)
if not exist "course-service\node_modules" (
    echo Installing course-service dependencies...
    cd course-service && npm install && cd ..
)
if not exist "payment-service\node_modules" (
    echo Installing payment-service dependencies...
    cd payment-service && npm install && cd ..
)
if not exist "analytics-service\node_modules" (
    echo Installing analytics-service dependencies...
    cd analytics-service && npm install && cd ..
)
if not exist "api-gateway\node_modules" (
    echo Installing api-gateway dependencies...
    cd api-gateway && npm install && cd ..
)
goto :eof

:start-services
echo ℹ️  Starting services with hot reload...
if not exist "logs" mkdir logs

REM Start services in background
start /b cmd /c "cd auth-service && npm run dev > ..\logs\auth-service.log 2>&1"
start /b cmd /c "cd course-service && npm run dev > ..\logs\course-service.log 2>&1"
start /b cmd /c "cd payment-service && npm run dev > ..\logs\payment-service.log 2>&1"
start /b cmd /c "cd analytics-service && npm run dev > ..\logs\analytics-service.log 2>&1"
start /b cmd /c "cd api-gateway && npm run dev > ..\logs\api-gateway.log 2>&1"

echo ✅ All services started in development mode
echo ℹ️  Services are running with hot reload enabled
echo ℹ️  Logs are available in logs\ directory
goto :eof

:setup-test-data
echo ℹ️  Setting up test data...
timeout /t 15 /nobreak >nul
node scripts\seed-test-data.js
node scripts\generate-test-tokens.js
echo ✅ Test data setup completed
goto :eof

:show-status
echo ℹ️  Service Status:
echo ====================
echo.
echo 📊 Databases:
docker-compose ps mongo-auth mongo-course mongo-payment mongo-analytics
echo.
echo 🚀 Services:
tasklist /fi "imagename eq node.exe" /fo table >nul 2>&1
if errorlevel 1 (
    echo   ❌ No Node.js processes running
) else (
    echo   ✅ Node.js processes running
    tasklist /fi "imagename eq node.exe" /fo table | findstr node.exe
)
echo.
echo 🌐 Endpoints:
echo   API Gateway:       http://localhost:3000
echo   Auth Service:      http://localhost:3001
echo   Course Service:    http://localhost:3002
echo   Payment Service:   http://localhost:3003
echo   Analytics Service: http://localhost:3004
goto :eof

:help
echo 🔧 Development Setup Commands:
echo   start      - Start databases and all services in dev mode
echo   stop       - Stop all services and databases
echo   restart    - Restart development services only
echo   status     - Show status of all services
echo   db-only    - Start only databases
echo   test-data  - Setup test data only
echo.
echo Examples:
echo   scripts\dev-setup.bat start
echo   scripts\dev-setup.bat status
echo   scripts\dev-setup.bat stop
goto :end

:end
endlocal
