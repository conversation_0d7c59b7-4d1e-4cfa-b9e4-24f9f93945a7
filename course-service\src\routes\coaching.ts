import { Router } from 'express';
import { body, query } from 'express-validator';
import { CoachingController } from '../controllers/CoachingController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const coachingController = new CoachingController();

// Create coaching session (tutors only)
router.post('/',
  authenticateToken,
  body('courseId').notEmpty(),
  body('topic').trim().isLength({ min: 5, max: 200 }),
  body('date').isISO8601(),
  body('meetingLink').isURL(),
  body('maxStudents').optional().isInt({ min: 1, max: 100 }),
  validateRequest,
  coachingController.createCoachingSession
);

// Get coaching sessions
router.get('/',
  authenticateToken,
  query('courseId').optional(),
  query('status').optional().isIn(['scheduled', 'in_progress', 'completed', 'cancelled']),
  validateRequest,
  coachingController.getCoachingSessions
);

// Get available coaching sessions for students
router.get('/available',
  authenticateToken,
  coachingController.getAvailableCoachingSessions
);

// Enroll in coaching session
router.post('/:sessionId/enroll',
  authenticateToken,
  coachingController.enrollInCoachingSession
);

// Update coaching session
router.put('/:sessionId',
  authenticateToken,
  body('topic').optional().trim().isLength({ min: 5, max: 200 }),
  body('date').optional().isISO8601(),
  body('meetingLink').optional().isURL(),
  body('maxStudents').optional().isInt({ min: 1, max: 100 }),
  body('status').optional().isIn(['scheduled', 'in_progress', 'completed', 'cancelled']),
  validateRequest,
  coachingController.updateCoachingSession
);

export default router;