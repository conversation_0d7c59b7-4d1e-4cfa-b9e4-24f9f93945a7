import axios from 'axios';
import FormData from 'form-data';
import { logger } from '../utils/logger';

export class BunnyService {
  private apiKey: string;
  private storageZone: string;
  private cdnHostname: string;
  private baseUrl = 'https://api.bunny.net';

  constructor() {
    this.apiKey = process.env.BUNNY_API_KEY || '';
    this.storageZone = process.env.BUNNY_STORAGE_ZONE || '';
    this.cdnHostname = process.env.BUNNY_CDN_HOSTNAME || '';

    if (!this.apiKey || !this.storageZone || !this.cdnHostname) {
      logger.warn('Bunny.net configuration incomplete. Video features may not work properly.');
    }
  }

  async uploadVideo(file: Buffer, fileName: string): Promise<{ videoId: string; videoUrl: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file, fileName);

      const response = await axios.post(`${this.baseUrl}/library/videos`, formData, {
        headers: {
          'AccessKey': this.apiKey,
          ...formData.getHeaders()
        }
      });

      const videoId = response.data.guid;
      const videoUrl = `https://${this.cdnHostname}/${videoId}/playlist.m3u8`;

      logger.info(`Video uploaded to Bunny.net: ${fileName} -> ${videoId}`);

      return { videoId, videoUrl };

    } catch (error) {
      logger.error('Bunny.net upload error:', error);
      throw new Error('Failed to upload video to Bunny.net');
    }
  }

  async getSecureStreamingUrl(videoId: string, expirationHours: number = 24): Promise<string> {
    try {
      // Generate secure URL with expiration
      const expirationTime = Math.floor(Date.now() / 1000) + (expirationHours * 3600);
      const secureUrl = `https://${this.cdnHostname}/${videoId}/playlist.m3u8?expires=${expirationTime}`;

      return secureUrl;

    } catch (error) {
      logger.error('Generate secure URL error:', error);
      throw new Error('Failed to generate secure streaming URL');
    }
  }

  async deleteVideo(videoId: string): Promise<void> {
    try {
      await axios.delete(`${this.baseUrl}/library/videos/${videoId}`, {
        headers: {
          'AccessKey': this.apiKey
        }
      });

      logger.info(`Video deleted from Bunny.net: ${videoId}`);

    } catch (error) {
      logger.error('Bunny.net delete error:', error);
      throw new Error('Failed to delete video from Bunny.net');
    }
  }

  async getVideoInfo(videoId: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/library/videos/${videoId}`, {
        headers: {
          'AccessKey': this.apiKey
        }
      });

      return response.data;

    } catch (error) {
      logger.error('Get video info error:', error);
      throw new Error('Failed to get video information');
    }
  }
}