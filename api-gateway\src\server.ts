import express from "express";
import { createProxyMiddleware } from "http-proxy-middleware";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import { logger } from "./utils/logger";
import { authenticateToken } from "./middleware/auth";

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || [
      "http://localhost:3000",
    ],
    credentials: true,
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for gateway
  message: "Too many requests from this IP, please try again later.",
});

app.use(limiter);
app.use(express.json());

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    service: "api-gateway",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Service URLs
const services = {
  auth: process.env.AUTH_SERVICE_URL || "http://auth-service:3001",
  course: process.env.COURSE_SERVICE_URL || "http://course-service:3002",
  payment: process.env.PAYMENT_SERVICE_URL || "http://payment-service:3003",
  analytics:
    process.env.ANALYTICS_SERVICE_URL || "http://analytics-service:3004",
};

// Auth Service Proxy
app.use(
  "/api/auth",
  createProxyMiddleware({
    target: services.auth,
    changeOrigin: true,
    pathRewrite: {
      "^/api/auth": "/api/auth",
    },
    onError: (err, req, res) => {
      logger.error("Auth service proxy error:", err);
      res.status(503).json({ error: "Auth service unavailable" });
    },
  })
);

app.use(
  "/api/users",
  createProxyMiddleware({
    target: services.auth,
    changeOrigin: true,
    pathRewrite: {
      "^/api/users": "/api/users",
    },
    onError: (err, req, res) => {
      logger.error("Auth service proxy error:", err);
      res.status(503).json({ error: "Auth service unavailable" });
    },
  })
);

// Course Service Proxy
app.use(
  "/api/courses",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/courses": "/api/courses",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

app.use(
  "/api/lessons",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/lessons": "/api/lessons",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

app.use(
  "/api/assignments",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/assignments": "/api/assignments",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

app.use(
  "/api/progress",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/progress": "/api/progress",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

app.use(
  "/api/certificates",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/certificates": "/api/certificates",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

app.use(
  "/api/coaching",
  createProxyMiddleware({
    target: services.course,
    changeOrigin: true,
    pathRewrite: {
      "^/api/coaching": "/api/coaching",
    },
    onError: (err, req, res) => {
      logger.error("Course service proxy error:", err);
      res.status(503).json({ error: "Course service unavailable" });
    },
  })
);

// Payment Service Proxy
app.use(
  "/api/payments",
  createProxyMiddleware({
    target: services.payment,
    changeOrigin: true,
    pathRewrite: {
      "^/api/payments": "/api/payments",
    },
    onError: (err, req, res) => {
      logger.error("Payment service proxy error:", err);
      res.status(503).json({ error: "Payment service unavailable" });
    },
  })
);

app.use(
  "/api/subscriptions",
  createProxyMiddleware({
    target: services.payment,
    changeOrigin: true,
    pathRewrite: {
      "^/api/subscriptions": "/api/subscriptions",
    },
    onError: (err, req, res) => {
      logger.error("Payment service proxy error:", err);
      res.status(503).json({ error: "Payment service unavailable" });
    },
  })
);

app.use(
  "/api/webhooks",
  createProxyMiddleware({
    target: services.payment,
    changeOrigin: true,
    pathRewrite: {
      "^/api/webhooks": "/api/webhooks",
    },
    onError: (err, req, res) => {
      logger.error("Payment service proxy error:", err);
      res.status(503).json({ error: "Payment service unavailable" });
    },
  })
);

// Analytics Service Proxy
app.use(
  "/api/analytics",
  createProxyMiddleware({
    target: services.analytics,
    changeOrigin: true,
    pathRewrite: {
      "^/api/analytics": "/api/analytics",
    },
    onError: (err, req, res) => {
      logger.error("Analytics service proxy error:", err);
      res.status(503).json({ error: "Analytics service unavailable" });
    },
  })
);

app.use(
  "/api/reports",
  createProxyMiddleware({
    target: services.analytics,
    changeOrigin: true,
    pathRewrite: {
      "^/api/reports": "/api/reports",
    },
    onError: (err, req, res) => {
      logger.error("Analytics service proxy error:", err);
      res.status(503).json({ error: "Analytics service unavailable" });
    },
  })
);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({ error: "Route not found" });
});

// Error handler
app.use(
  (
    error: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Gateway error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
);

app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`);
  logger.info("Service endpoints:", services);
});

export default app;
