#!/usr/bin/env node

/**
 * Simple test script untuk testing tanpa Firebase
 * Jalankan dengan: node scripts/simple-test.js
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:3001'; // Direct ke auth service

async function testDirectConnection() {
  console.log('🧪 Testing Direct Connection to Services');
  console.log('='.repeat(60));

  // Test 1: Health check
  try {
    console.log('\n1. Testing Health Check...');
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', response.status);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  // Test 2: Database connection via direct endpoint
  try {
    console.log('\n2. Testing Database Connection...');
    // Try to get users without auth (should fail but show connection works)
    const response = await axios.get(`${BASE_URL}/api/users`);
    console.log('✅ Database connection works');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Database connection works (got expected 401 auth error)');
    } else {
      console.log('❌ Database connection failed:', error.message);
    }
  }

  // Test 3: Test with JWT token
  try {
    console.log('\n3. Testing with JWT Token...');
    
    // Load tokens
    let tokens = {};
    if (fs.existsSync('test-tokens.json')) {
      tokens = JSON.parse(fs.readFileSync('test-tokens.json', 'utf8'));
    }

    if (tokens.admin && tokens.admin[0]) {
      const token = tokens.admin[0].token;
      const response = await axios.get(`${BASE_URL}/api/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ JWT Authentication works');
      console.log('📋 Users found:', response.data.data?.length || 0);
    } else {
      console.log('⚠️  No admin token found. Run "npm run gen-tokens" first.');
    }
  } catch (error) {
    console.log('❌ JWT test failed:', error.response?.status, error.response?.data || error.message);
  }
}

async function testDatabaseDirect() {
  console.log('\n🗄️  Testing Database Direct Connection');
  console.log('='.repeat(60));

  const mongoose = require('mongoose');
  
  try {
    await mongoose.connect('mongodb://localhost:27017/time_course_auth');
    console.log('✅ Direct MongoDB connection successful');
    
    // Test user count
    const User = mongoose.model('User', new mongoose.Schema({
      email: String,
      role: String,
      displayName: String
    }));
    
    const userCount = await User.countDocuments();
    console.log(`📊 Total users in database: ${userCount}`);
    
    const testUsers = await User.find({ email: { $regex: '@test\.com$' } });
    console.log(`🧪 Test users found: ${testUsers.length}`);
    
    testUsers.forEach(user => {
      console.log(`  👤 ${user.role?.toUpperCase().padEnd(8)} | ${user.email}`);
    });
    
    await mongoose.disconnect();
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
  }
}

async function testWithCurl() {
  console.log('\n🌐 cURL Test Examples');
  console.log('='.repeat(60));
  
  // Load tokens
  let tokens = {};
  if (fs.existsSync('test-tokens.json')) {
    tokens = JSON.parse(fs.readFileSync('test-tokens.json', 'utf8'));
  }

  if (tokens.admin && tokens.admin[0]) {
    const token = tokens.admin[0].token;
    
    console.log('\n📋 Test these cURL commands manually:');
    console.log('\n1. Get all users (Admin):');
    console.log(`curl -X GET "http://localhost:3001/api/users" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -H "Content-Type: application/json"`);
    
    console.log('\n2. Get profile:');
    console.log(`curl -X GET "http://localhost:3001/api/auth/profile" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -H "Content-Type: application/json"`);
  }

  if (tokens.student && tokens.student[0]) {
    const token = tokens.student[0].token;
    
    console.log('\n3. Student get profile:');
    console.log(`curl -X GET "http://localhost:3001/api/auth/profile" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -H "Content-Type: application/json"`);
    
    console.log('\n4. Student try get users (should fail):');
    console.log(`curl -X GET "http://localhost:3001/api/users" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -H "Content-Type: application/json"`);
  }
}

async function main() {
  console.log('🚀 Simple Testing Suite');
  console.log('='.repeat(60));
  
  await testDatabaseDirect();
  await testDirectConnection();
  await testWithCurl();
  
  console.log('\n✅ Testing completed!');
  console.log('\n💡 Next steps:');
  console.log('1. Fix Firebase configuration in .env file');
  console.log('2. Start auth-service properly');
  console.log('3. Test the cURL commands above');
  console.log('4. Use "npm run test-api" for automated testing');
}

if (require.main === module) {
  main().catch(console.error);
}
