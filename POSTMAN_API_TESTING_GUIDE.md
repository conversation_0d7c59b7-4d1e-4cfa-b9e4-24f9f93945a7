# Time Course Platform - Postman API Testing Guide

This guide provides comprehensive instructions for testing the Time Course Platform APIs using Postman.

## 📁 Files Included

- `postman-collection.json` - Complete Postman collection with all API endpoints
- `postman-environment.json` - Environment variables with test tokens and configuration
- `POSTMAN_API_TESTING_GUIDE.md` - This guide

## 🚀 Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Import both files:
   - `postman-collection.json`
   - `postman-environment.json`
4. Select the "Time Course Platform Environment" from the environment dropdown

### 2. Start the Services

Before testing, ensure all services are running:

```bash
# Start all services
scripts\start-all-services.bat

# Or start individually
cd api-gateway && npm run dev
cd auth-service && npm run dev
cd course-service && npm run dev
cd payment-service && npm run dev
cd analytics-service && npm run dev
```

### 3. Verify Services are Running

Test the health check endpoint:
- **GET** `http://localhost:3000/health`

## 🔐 Authentication

The collection includes pre-configured test tokens for different user roles:

### Available Test Users

| Role | Email | Token Variable | User ID |
|------|-------|----------------|---------|
| Admin | <EMAIL> | `{{admin_token}}` | 68d39dba545d3c08136cb8db |
| Tutor | <EMAIL> | `{{tutor_token}}` | 68d39dba545d3c08136cb8dc |
| Student | <EMAIL> | `{{student_token}}` | 68d39dba545d3c08136cb8de |

### Using Authentication

1. **Set the auth_token variable**: Copy the appropriate token to `{{auth_token}}`
2. **Use role-specific tokens**: Many requests already use role-specific tokens like `{{admin_token}}`
3. **Authorization header**: Most endpoints use `Bearer {{auth_token}}` or role-specific tokens

## 📋 API Endpoints Overview

### 🏥 Health Check
- **GET** `/health` - Check API Gateway status

### 🔐 Authentication
- **POST** `/api/auth/firebase-login` - Login with Firebase token
- **GET** `/api/auth/profile` - Get current user profile
- **PUT** `/api/auth/profile` - Update user profile
- **POST** `/api/auth/create-tutor` - Create tutor account (Admin only)
- **POST** `/api/auth/logout` - Logout and invalidate token

### 👥 User Management
- **GET** `/api/users` - Get all users with pagination (Admin only)
- **GET** `/api/users/{userId}` - Get user by ID
- **DELETE** `/api/users/{userId}` - Delete user (Admin only)

### 📚 Course Management
- **POST** `/api/courses` - Create course (Tutor/Admin)
- **GET** `/api/courses` - Get all courses with filters
- **GET** `/api/courses/{courseId}` - Get course by ID
- **PUT** `/api/courses/{courseId}` - Update course
- **DELETE** `/api/courses/{courseId}` - Delete course
- **POST** `/api/courses/{courseId}/publish` - Publish course
- **GET** `/api/courses/{courseId}/progress` - Get student progress

### 🎓 Lessons & Learning
- **GET** `/api/lessons/{courseId}/modules/{moduleId}/lessons/{lessonId}/video` - Get video URL
- **GET** `/api/lessons/{courseId}/modules/{moduleId}/lessons/{lessonId}/assignments` - Get assignments
- **POST** `/api/lessons/{courseId}/modules/{moduleId}/lessons/{lessonId}/progress` - Update progress

### 💳 Payments
- **POST** `/api/payments` - Create payment
- **GET** `/api/payments/{paymentId}` - Get payment status
- **GET** `/api/payments/student/me` - Get student payments

### 📝 Subscriptions
- **POST** `/api/subscriptions` - Create subscription
- **GET** `/api/subscriptions/student/{studentId}` - Get student subscriptions
- **GET** `/api/subscriptions/access/{courseId}` - Check course access
- **GET** `/api/subscriptions/terminated` - Get terminated students

### 📊 Analytics
- **GET** `/api/analytics/overview` - Get overview analytics
- **GET** `/api/analytics/range` - Get analytics by date range
- **GET** `/api/analytics/tutor/{tutorId}` - Get tutor analytics
- **POST** `/api/analytics/refresh` - Refresh analytics

### 📈 Reports
- **GET** `/api/reports/users` - Generate user report
- **GET** `/api/reports/courses` - Generate course report
- **GET** `/api/reports/revenue` - Generate revenue report
- **GET** `/api/reports/tutors` - Generate tutor performance report

### 🎯 Coaching
- **POST** `/api/coaching` - Create coaching session (Tutor)
- **GET** `/api/coaching` - Get coaching sessions
- **GET** `/api/coaching/available` - Get available sessions (Student)
- **POST** `/api/coaching/{sessionId}/enroll` - Enroll in session

### 🏆 Certificates
- **POST** `/api/certificates/generate/{courseId}` - Generate certificate
- **GET** `/api/certificates/verify/{verificationCode}` - Verify certificate (Public)
- **GET** `/api/certificates/student/me` - Get student certificates

### 🔗 Webhooks
- **POST** `/api/webhooks/xendit/payment` - Xendit payment webhook
- **POST** `/api/webhooks/xendit/invoice` - Xendit invoice webhook

## 🧪 Testing Workflow

### 1. Basic Authentication Test
1. Use **Health Check** to verify services
2. Test **Get Profile** with different role tokens
3. Verify role-based access control

### 2. Course Management Flow
1. **Admin/Tutor**: Create a course
2. **Admin/Tutor**: Publish the course
3. **Student**: View available courses
4. **Student**: Check course access

### 3. Payment & Subscription Flow
1. **Student**: Create payment for a course
2. **Student**: Check payment status
3. **Student**: Create subscription after payment
4. **Student**: Verify course access

### 4. Learning Flow
1. **Student**: Get lesson video URL
2. **Student**: Update video progress
3. **Student**: Get lesson assignments
4. **Student**: Generate certificate (after completion)

## 🔧 Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API Gateway URL | `http://localhost:3000` |
| `auth_token` | Current auth token | Set based on role |
| `admin_token` | Admin user token | Pre-configured |
| `tutor_token` | Tutor user token | Pre-configured |
| `student_token` | Student user token | Pre-configured |
| `sample_course_id` | Test course ID | Replace with actual ID |
| `sample_payment_id` | Test payment ID | Replace with actual ID |

## 🚨 Important Notes

1. **Token Expiration**: Test tokens expire after 7 days. Generate new ones using `scripts/generate-test-tokens.js`
2. **Service Dependencies**: Ensure all services are running before testing
3. **Database State**: Some tests may require specific data in the database
4. **Role Permissions**: Each endpoint has specific role requirements
5. **Rate Limiting**: API Gateway has rate limiting enabled

## 🔄 Regenerating Test Tokens

If tokens expire, regenerate them:

```bash
node scripts/generate-test-tokens.js
```

Then update the environment variables in Postman with the new tokens.

## 📞 Support

For issues or questions:
1. Check service logs in the `logs/` directory
2. Verify all services are running with `scripts/check-services.bat`
3. Review the API documentation in the codebase
