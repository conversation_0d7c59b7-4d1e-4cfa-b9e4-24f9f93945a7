export interface User {
  _id: string;
  uid: string; // Firebase UID
  email: string;
  displayName: string;
  photoURL?: string;
  role: UserRole;
  status: UserStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  STUDENT = 'student',
  TUTOR = 'tutor',
  ADMIN = 'admin'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface Course {
  _id: string;
  title: string;
  description: string;
  category: string;
  tutorId: string;
  duration: number; // in days
  price: number;
  currency: string;
  status: CourseStatus;
  modules: Module[];
  createdAt: Date;
  updatedAt: Date;
}

export enum CourseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

export interface Module {
  _id: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
}

export interface Lesson {
  _id: string;
  title: string;
  description: string;
  order: number;
  videoUrl?: string;
  videoId?: string; // Bunny.net video ID
  documents: Document[];
  assignments: Assignment[];
  duration: number; // in seconds
}

export interface Document {
  _id: string;
  title: string;
  type: DocumentType;
  url: string;
  size: number;
}

export enum DocumentType {
  PDF = 'pdf',
  PPT = 'ppt',
  DOCX = 'docx',
  IMAGE = 'image'
}

export interface Assignment {
  _id: string;
  title: string;
  triggerTimestamp: number; // in seconds from video start
  timeLimit: number; // in seconds
  questions: Question[];
}

export interface Question {
  _id: string;
  question: string;
  type: QuestionType;
  correctAnswer: string;
  points: number;
}

export enum QuestionType {
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  MULTIPLE_CHOICE = 'multiple_choice'
}

export interface Subscription {
  _id: string;
  studentId: string;
  courseId: string;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  failureCount: number;
  maxFailures: number;
  progress: Progress;
  paymentId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
  CANCELLED = 'cancelled'
}

export interface Progress {
  completedModules: string[];
  completedLessons: string[];
  completedAssignments: string[];
  currentModule: number;
  currentLesson: number;
  completionPercentage: number;
}

export interface Payment {
  _id: string;
  studentId: string;
  courseId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  xenditPaymentId: string;
  xenditInvoiceId: string;
  paymentMethod: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface CoachingSession {
  _id: string;
  tutorId: string;
  courseId: string;
  topic: string;
  date: Date;
  meetingLink: string;
  maxStudents: number;
  enrolledStudents: string[];
  status: CoachingSessionStatus;
  createdAt: Date;
}

export enum CoachingSessionStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface AssignmentSubmission {
  _id: string;
  studentId: string;
  assignmentId: string;
  lessonId: string;
  courseId: string;
  answers: AnswerSubmission[];
  score: number;
  maxScore: number;
  passed: boolean;
  submittedAt: Date;
}

export interface AnswerSubmission {
  questionId: string;
  answer: string;
  isCorrect: boolean;
  points: number;
}

export interface Certificate {
  _id: string;
  studentId: string;
  courseId: string;
  verificationCode: string;
  issuedAt: Date;
  downloadUrl: string;
}

// Certificate Generation Types
export interface CertificateTemplate {
  _id: string;
  name: string;
  templateUrl: string;
  fields: CertificateField[];
}

export interface CertificateField {
  name: string;
  type: 'text' | 'date' | 'signature';
  x: number;
  y: number;
  fontSize: number;
  fontFamily: string;
  color: string;
}

// Analytics Types
export interface Analytics {
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  popularCourses: PopularCourse[];
  failureRate: number;
  completionRate: number;
  tutorStats: TutorStats[];
}

export interface PopularCourse {
  courseId: string;
  title: string;
  enrollments: number;
  revenue: number;
}

export interface TutorStats {
  tutorId: string;
  name: string;
  totalStudents: number;
  totalCourses: number;
  averageRating: number;
  revenue: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Request Types
export interface CreateCourseRequest {
  title: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  currency: string;
}

export interface CreateSubscriptionRequest {
  courseId: string;
  paymentId: string;
}

export interface CreatePaymentRequest {
  courseId: string;
  amount: number;
  currency: string;
}

export interface SubmitAssignmentRequest {
  assignmentId: string;
  lessonId: string;
  answers: AnswerSubmission[];
}