@echo off
REM Script untuk menjalankan semua services tanpa Docker
REM Jalankan dengan: scripts\start-all-services.bat

echo 🚀 Starting All Services (Non-Docker)
echo =====================================

REM Create logs directory
if not exist "logs" mkdir logs

REM Kill existing node processes
echo 🧹 Cleaning up existing processes...
taskkill /f /im node.exe >nul 2>&1

REM Start Auth Service (Port 3001)
echo 🔐 Starting Auth Service on port 3001...
start "Auth Service" cmd /c "cd auth-service && npm run dev > ../logs/auth-service.log 2>&1"

REM Wait a bit
timeout /t 3 /nobreak >nul

REM Start Course Service (Port 3002)
echo 📚 Starting Course Service on port 3002...
start "Course Service" cmd /c "cd course-service && npm run dev > ../logs/course-service.log 2>&1"

REM Wait a bit
timeout /t 2 /nobreak >nul

REM Start Payment Service (Port 3003)
echo 💳 Starting Payment Service on port 3003...
start "Payment Service" cmd /c "cd payment-service && npm run dev > ../logs/payment-service.log 2>&1"

REM Wait a bit
timeout /t 2 /nobreak >nul

REM Start Analytics Service (Port 3004)
echo 📊 Starting Analytics Service on port 3004...
start "Analytics Service" cmd /c "cd analytics-service && npm run dev > ../logs/analytics-service.log 2>&1"

REM Wait a bit
timeout /t 2 /nobreak >nul

REM Start API Gateway (Port 3000)
echo 🌐 Starting API Gateway on port 3000...
start "API Gateway" cmd /c "cd api-gateway && npm run dev > ../logs/api-gateway.log 2>&1"

echo.
echo ✅ All services are starting up!
echo.
echo 📋 Service Endpoints:
echo   🌐 API Gateway:       http://localhost:3000
echo   🔐 Auth Service:      http://localhost:3001
echo   📚 Course Service:    http://localhost:3002
echo   💳 Payment Service:   http://localhost:3003
echo   📊 Analytics Service: http://localhost:3004
echo.
echo 📁 Logs are available in logs/ directory
echo 🔍 Use "scripts\check-services.bat" to check status
echo 🛑 Use "scripts\stop-all-services.bat" to stop all services
echo.
echo ⏳ Please wait 30-60 seconds for all services to fully start...

pause
