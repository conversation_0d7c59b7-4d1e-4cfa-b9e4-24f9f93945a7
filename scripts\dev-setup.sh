#!/bin/bash

# Development setup script untuk menghindari rebuild container terus-menerus
# Jalankan dengan: ./scripts/dev-setup.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "Docker is running"
}

# Start only databases (untuk development tanpa rebuild)
start_databases() {
    print_info "Starting only MongoDB databases..."
    
    docker-compose up -d \
        mongo-auth \
        mongo-course \
        mongo-payment \
        mongo-analytics
    
    print_status "Databases started successfully"
    print_info "Waiting for databases to be ready..."
    sleep 10
}

# Start services in development mode (dengan hot reload)
start_dev_services() {
    print_info "Starting services in development mode..."
    
    # Install dependencies jika belum ada
    if [ ! -d "auth-service/node_modules" ]; then
        print_info "Installing auth-service dependencies..."
        cd auth-service && npm install && cd ..
    fi
    
    if [ ! -d "course-service/node_modules" ]; then
        print_info "Installing course-service dependencies..."
        cd course-service && npm install && cd ..
    fi
    
    if [ ! -d "payment-service/node_modules" ]; then
        print_info "Installing payment-service dependencies..."
        cd payment-service && npm install && cd ..
    fi
    
    if [ ! -d "analytics-service/node_modules" ]; then
        print_info "Installing analytics-service dependencies..."
        cd analytics-service && npm install && cd ..
    fi
    
    if [ ! -d "api-gateway/node_modules" ]; then
        print_info "Installing api-gateway dependencies..."
        cd api-gateway && npm install && cd ..
    fi
    
    # Start services dengan nodemon (hot reload)
    print_info "Starting services with hot reload..."
    
    # Start in background
    cd auth-service && npm run dev > ../logs/auth-service.log 2>&1 &
    AUTH_PID=$!
    
    cd ../course-service && npm run dev > ../logs/course-service.log 2>&1 &
    COURSE_PID=$!
    
    cd ../payment-service && npm run dev > ../logs/payment-service.log 2>&1 &
    PAYMENT_PID=$!
    
    cd ../analytics-service && npm run dev > ../logs/analytics-service.log 2>&1 &
    ANALYTICS_PID=$!
    
    cd ../api-gateway && npm run dev > ../logs/api-gateway.log 2>&1 &
    GATEWAY_PID=$!
    
    cd ..
    
    # Save PIDs for later cleanup
    echo "$AUTH_PID $COURSE_PID $PAYMENT_PID $ANALYTICS_PID $GATEWAY_PID" > .dev-pids
    
    print_status "All services started in development mode"
    print_info "Services are running with hot reload enabled"
    print_info "Logs are available in logs/ directory"
}

# Setup test data
setup_test_data() {
    print_info "Setting up test data..."
    
    # Wait for services to be ready
    sleep 15
    
    # Create test users
    node scripts/seed-test-data.js
    
    # Generate test tokens
    node scripts/generate-test-tokens.js
    
    print_status "Test data setup completed"
}

# Stop development services
stop_dev_services() {
    print_info "Stopping development services..."
    
    if [ -f .dev-pids ]; then
        PIDS=$(cat .dev-pids)
        for pid in $PIDS; do
            if kill -0 $pid 2>/dev/null; then
                kill $pid
                print_info "Stopped process $pid"
            fi
        done
        rm .dev-pids
    fi
    
    print_status "Development services stopped"
}

# Show service status
show_status() {
    print_info "Service Status:"
    echo "===================="
    
    # Check databases
    echo "📊 Databases:"
    docker-compose ps mongo-auth mongo-course mongo-payment mongo-analytics
    
    echo ""
    echo "🚀 Services:"
    if [ -f .dev-pids ]; then
        PIDS=$(cat .dev-pids)
        SERVICES=("auth-service" "course-service" "payment-service" "analytics-service" "api-gateway")
        i=0
        for pid in $PIDS; do
            if kill -0 $pid 2>/dev/null; then
                echo "  ✅ ${SERVICES[$i]} (PID: $pid)"
            else
                echo "  ❌ ${SERVICES[$i]} (Not running)"
            fi
            ((i++))
        done
    else
        echo "  ❌ No development services running"
    fi
    
    echo ""
    echo "🌐 Endpoints:"
    echo "  API Gateway:      http://localhost:3000"
    echo "  Auth Service:     http://localhost:3001"
    echo "  Course Service:   http://localhost:3002"
    echo "  Payment Service:  http://localhost:3003"
    echo "  Analytics Service: http://localhost:3004"
}

# Show logs
show_logs() {
    SERVICE=$1
    if [ -z "$SERVICE" ]; then
        print_info "Available logs:"
        ls -la logs/ 2>/dev/null || echo "No logs directory found"
        return
    fi
    
    LOG_FILE="logs/${SERVICE}.log"
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        print_error "Log file not found: $LOG_FILE"
    fi
}

# Create logs directory
mkdir -p logs

# Main command handler
case "$1" in
    "start")
        check_docker
        start_databases
        start_dev_services
        setup_test_data
        show_status
        ;;
    "stop")
        stop_dev_services
        docker-compose down
        ;;
    "restart")
        stop_dev_services
        start_dev_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs $2
        ;;
    "db-only")
        check_docker
        start_databases
        print_status "Only databases started. Use 'npm run dev' in each service directory for development."
        ;;
    "test-data")
        setup_test_data
        ;;
    *)
        echo "🔧 Development Setup Commands:"
        echo "  start      - Start databases and all services in dev mode"
        echo "  stop       - Stop all services and databases"
        echo "  restart    - Restart development services only"
        echo "  status     - Show status of all services"
        echo "  logs [svc] - Show logs for specific service"
        echo "  db-only    - Start only databases"
        echo "  test-data  - Setup test data only"
        echo ""
        echo "Examples:"
        echo "  ./scripts/dev-setup.sh start"
        echo "  ./scripts/dev-setup.sh logs auth-service"
        echo "  ./scripts/dev-setup.sh status"
        ;;
esac
