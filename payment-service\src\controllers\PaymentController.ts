import { Request, Response } from "express";
import { PaymentModel } from "../models/Payment";
import { UserRole, PaymentStatus } from "../../../shared/types";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS } from "../../../shared/utils/constants";
import { logger } from "../utils/logger";
import { XenditService } from "../services/XenditService";

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class PaymentController {
  private xenditService = new XenditService();

  async createPayment(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, amount, currency = "IDR" } = req.body;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res
          .status(HTTP_STATUS.UNAUTHORIZED)
          .json(createApiResponse(false, "Student ID required"));
      }

      // Check if student already has active subscription for this course
      const existingPayment = await PaymentModel.findOne({
        studentId,
        courseId,
        status: { $in: [PaymentStatus.PENDING, PaymentStatus.PAID] },
      });

      if (existingPayment) {
        return res
          .status(HTTP_STATUS.CONFLICT)
          .json(
            createApiResponse(false, "Payment already exists for this course")
          );
      }

      // Create payment record
      const payment = new PaymentModel({
        studentId,
        courseId,
        amount,
        currency,
        status: PaymentStatus.PENDING,
      });

      await payment.save();

      // Create Xendit invoice
      const invoice = await this.xenditService.createInvoice({
        externalId: payment._id.toString(),
        amount,
        currency,
        description: `Course Payment - ${courseId}`,
        customerEmail: req.user?.email || "",
      });

      // Update payment with Xendit details
      payment.xenditInvoiceId = invoice.id;
      await payment.save();

      logger.info(
        `Payment created for student ${studentId}: ${amount} ${currency}`
      );

      res.status(HTTP_STATUS.CREATED).json(
        createApiResponse(true, "Payment created successfully", {
          paymentId: payment._id,
          invoiceUrl: invoice.invoice_url,
          amount,
          currency,
          status: payment.status,
        })
      );
    } catch (error) {
      logger.error("Create payment error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to create payment",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getPaymentStatus(req: AuthenticatedRequest, res: Response) {
    try {
      const { paymentId } = req.params;
      const studentId = req.user?.userId;

      const payment = await PaymentModel.findById(paymentId);

      if (!payment) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Payment not found"));
      }

      // Check if user owns this payment or is admin
      if (
        req.user?.role !== UserRole.ADMIN &&
        payment.studentId !== studentId
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      res.json(
        createApiResponse(
          true,
          "Payment status retrieved successfully",
          payment
        )
      );
    } catch (error) {
      logger.error("Get payment status error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve payment status",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getStudentPayments(req: AuthenticatedRequest, res: Response) {
    try {
      const studentId = req.user?.userId;

      if (!studentId) {
        return res
          .status(HTTP_STATUS.UNAUTHORIZED)
          .json(createApiResponse(false, "Student ID required"));
      }

      const payments = await PaymentModel.find({ studentId }).sort({
        createdAt: -1,
      });

      res.json(
        createApiResponse(
          true,
          "Student payments retrieved successfully",
          payments
        )
      );
    } catch (error) {
      logger.error("Get student payments error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve student payments",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }
}
