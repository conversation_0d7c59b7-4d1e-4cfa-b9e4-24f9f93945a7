import mongoose, { Document, Schema } from "mongoose";
import { Progress as IProgress } from "../../../shared/types";

export interface ProgressDocument extends Omit<IProgress, "_id">, Document {
  studentId: string;
  courseId: string;
}

const progressSchema = new Schema<ProgressDocument>(
  {
    studentId: { type: String, required: true, index: true },
    courseId: { type: String, required: true, index: true },
    completedModules: [{ type: String }],
    completedLessons: [{ type: String }],
    completedAssignments: [{ type: String }],
    currentModule: { type: Number, default: 0 },
    currentLesson: { type: Number, default: 0 },
    completionPercentage: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Compound indexes
progressSchema.index({ studentId: 1, courseId: 1 }, { unique: true });

export const ProgressModel = mongoose.model<ProgressDocument>(
  "Progress",
  progressSchema
);
