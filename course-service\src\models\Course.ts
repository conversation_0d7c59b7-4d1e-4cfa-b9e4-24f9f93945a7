import mongoose, { Document, Schema } from "mongoose";
import {
  Course as I<PERSON>ourse,
  CourseStatus,
  Module,
  Lesson,
  Document as IDocument,
  Assignment,
  Question,
  DocumentType,
  QuestionType,
} from "../../../shared/types";

export interface CourseDocument extends Omit<ICourse, "_id">, Document {}

const questionSchema = new Schema({
  question: { type: String, required: true },
  type: { type: String, enum: Object.values(QuestionType), required: true },
  correctAnswer: { type: String, required: true },
  points: { type: Number, required: true, default: 1 },
});

const assignmentSchema = new Schema({
  title: { type: String, required: true },
  triggerTimestamp: { type: Number, required: true }, // seconds from video start
  timeLimit: { type: Number, required: true, default: 300 }, // 5 minutes default
  questions: [questionSchema],
});

const documentSchema = new Schema({
  title: { type: String, required: true },
  type: { type: String, enum: Object.values(DocumentType), required: true },
  url: { type: String, required: true },
  size: { type: Number, required: true },
});

const lessonSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  videoUrl: { type: String },
  videoId: { type: String }, // Bunny.net video ID
  documents: [documentSchema],
  assignments: [assignmentSchema],
  duration: { type: Number, required: true, default: 0 }, // in seconds
});

const moduleSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  lessons: [lessonSchema],
});

const courseSchema = new Schema<CourseDocument>(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    category: { type: String, required: true },
    tutorId: { type: String, required: true, index: true },
    duration: { type: Number, required: true, default: 90 }, // days
    price: { type: Number, required: true },
    currency: { type: String, required: true, default: "IDR" },
    status: {
      type: String,
      enum: Object.values(CourseStatus),
      default: CourseStatus.DRAFT,
    },
    modules: [moduleSchema],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Indexes
courseSchema.index({ tutorId: 1, status: 1 });
courseSchema.index({ category: 1, status: 1 });
courseSchema.index({ title: "text", description: "text" });

export const CourseModel = mongoose.model<CourseDocument>(
  "Course",
  courseSchema
);
