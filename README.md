# Time Course - English Online Course Platform

A comprehensive microservices-based e-learning platform specializing in subscription-based English language test preparation with strict evaluation systems and coaching mechanisms.

## 🏗️ Architecture Overview

The platform is built using microservices architecture with the following services:

### 1. Auth & User Service (Port: 3001)

- Firebase Authentication integration
- User management (Students, Tutors, Admins)
- JWT token management
- Role-based access control

### 2. Course & Learning Service (Port: 3002)

- Course creation and management
- Video streaming with Bunny.net
- Assignment system with timestamp triggers
- Progress tracking
- Content security (no downloads)

### 3. Payment & Subscription Service (Port: 3003)

- Xendit payment gateway integration
- Subscription management
- Time-limited access control
- Failure tracking and termination logic

### 4. Analytics & Reporting Service (Port: 3004)

- Cross-service data aggregation
- Admin and tutor dashboards
- Performance analytics
- Student progress reports

### 5. API Gateway (Port: 3000)

- Request routing and load balancing
- Authentication middleware
- Rate limiting
- CORS handling

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for development)
- MongoDB (handled by <PERSON><PERSON>)
- Firebase project with <PERSON><PERSON> SDK
- <PERSON>.net account for video streaming
- Xendit account for payments

### Environment Setup

1. Copy the environment template:

```bash
cp .env.example .env
```

2. Fill in your credentials in `.env`:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key

# Bunny.net Configuration
BUNNY_API_KEY=your-bunny-api-key
BUNNY_STORAGE_ZONE=your-storage-zone-name
BUNNY_CDN_HOSTNAME=your-cdn-hostname.b-cdn.net

# Xendit Configuration
XENDIT_SECRET_KEY=xnd_development_your-secret-key
XENDIT_WEBHOOK_TOKEN=your-webhook-verification-token

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### Development Setup

1. Start all services:

```bash
npm run dev
```

This command will:

- Build all Docker images with development configuration
- Start all microservices with hot reloading (nodemon)
- Launch MongoDB instances for each service
- Set up the API Gateway with proxy routing
- Mount source code volumes for real-time development

2. Or start in detached mode:

```bash
npm run dev:detached
```

3. View logs:

```bash
npm run logs
```

4. Stop all services:

```bash
npm run down
```

5. Clean up everything (containers, volumes, networks):

```bash
npm run clean
```

### ✅ Development Features

- **Hot Reloading**: All services automatically restart when code changes
- **Volume Mounting**: Source code is mounted for instant development feedback
- **Separate Databases**: Each service has its own MongoDB instance
- **Environment Variables**: Centralized configuration via .env file
- **Docker Compose**: Single command to start entire development environment

### Service URLs

- API Gateway: http://localhost:3000
- Auth Service: http://localhost:3001
- Course Service: http://localhost:3002
- Payment Service: http://localhost:3003
- Analytics Service: http://localhost:3004

## 🔐 Authentication Flow

1. **Student Registration**: Users sign up with Google OAuth via Firebase
2. **Tutor Creation**: Admin creates tutor accounts manually
3. **JWT Tokens**: Services use JWT for inter-service communication
4. **Role-based Access**: Endpoints protected by role middleware

## 📚 Core Features Implemented

### ✅ Completed Features

#### Auth & User Service

- Firebase Authentication integration
- JWT token generation and validation
- User CRUD operations
- Role-based access control
- Admin-only tutor creation
- User status management

#### Course Service Foundation

- Basic course model and structure
- Module and lesson organization
- Assignment framework with timestamp triggers
- Video upload integration with Bunny.net
- Content security measures

#### Payment Service Foundation

- Xendit integration setup
- Subscription model
- Payment webhook handling
- Subscription status tracking

#### Infrastructure

- Docker containerization for all services
- MongoDB setup for each service
- CI/CD pipeline with GitHub Actions
- API Gateway for request routing
- Comprehensive logging system
- Error handling middleware

### 🔄 Next Phase Implementation Required

The following features need to be implemented to complete the platform:

#### Course & Learning Service

1. **Video Player with Assignment Integration**

   - HLS/DASH streaming implementation
   - Video player that pauses for assignments
   - Timestamp-based assignment triggers
   - Disable seeking/skipping during assignments

2. **Assignment System**

   - Question bank implementation
   - Real-time grading system
   - Timer functionality
   - Score calculation and progress tracking

3. **Content Management**

   - PDF/PPT viewer without download
   - File upload and processing
   - Content security enhancements

4. **Progress Tracking**
   - Detailed progress calculation
   - Pace monitoring and warnings
   - Completion tracking

#### Payment & Subscription Service

1. **Subscription Logic**

   - Time-limited access enforcement
   - Automatic expiration handling
   - Failure count tracking (3 strikes rule)
   - Subscription termination logic

2. **Payment Processing**
   - Complete Xendit webhook implementation
   - Payment verification
   - Refund handling
   - Invoice generation

#### Analytics & Reporting Service

1. **Data Aggregation**

   - Cross-service data collection
   - Real-time analytics processing
   - Performance metrics calculation

2. **Dashboard Implementation**
   - Admin analytics dashboard
   - Tutor performance reports
   - Student progress analytics

#### Additional Features

1. **Coaching System**

   - Failed student identification
   - Group coaching session management
   - Meeting integration (Zoom/Google Meet)

2. **Certificate System**

   - Digital certificate generation
   - Verification code system
   - PDF certificate creation

3. **Notification System**

   - Email notifications
   - In-app notifications
   - Progress alerts

4. **Mobile API Support**
   - REST API optimization
   - Mobile-specific endpoints
   - Offline capability

## 📋 Implementation Priority

### Phase 1: Core Learning System

1. Complete video streaming with assignment integration
2. Implement assignment submission and grading
3. Build progress tracking system
4. Add subscription time-limit enforcement

### Phase 2: Business Logic

1. Implement 3-failure termination rule
2. Add pace monitoring and warnings
3. Build coaching session management
4. Create certificate generation system

### Phase 3: Analytics & Optimization

1. Complete analytics service
2. Build admin and tutor dashboards
3. Add performance monitoring
4. Implement caching strategies

### Phase 4: Enhancement & Scaling

1. Add mobile API endpoints
2. Implement real-time features
3. Add advanced security measures
4. Performance optimization

## 🔧 Development Commands

```bash
# Install dependencies for all services
./scripts/install-deps.sh

# Run tests for all services
./scripts/run-tests.sh

# Build all services
./scripts/build-all.sh

# Clean up containers and volumes
npm run clean
```

## 🏭 Production Deployment

The platform is configured for containerized deployment:

1. **Docker Hub**: Images are automatically built and pushed
2. **Environment Variables**: Configure production secrets
3. **Database**: Set up MongoDB clusters for each service
4. **Load Balancing**: Configure reverse proxy (Nginx/HAProxy)
5. **Monitoring**: Set up logging aggregation and monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes following TypeScript best practices
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Next Steps**: Continue implementation based on the priority phases outlined above. Each service has a solid foundation and can be extended incrementally.
