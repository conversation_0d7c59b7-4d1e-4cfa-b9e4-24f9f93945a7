@echo off
REM Script untuk stop semua services
REM Jalankan dengan: scripts\stop-all-services.bat

echo 🛑 Stopping All Services
echo ========================

echo 🧹 Killing all Node.js processes...
taskkill /f /im node.exe >nul 2>&1

if errorlevel 1 (
    echo   ℹ️  No Node.js processes were running
) else (
    echo   ✅ All Node.js processes stopped
)

echo.
echo 🔍 Checking if ports are free...

REM Check if ports are still in use
netstat -an | findstr ":3000 :3001 :3002 :3003 :3004" >nul
if errorlevel 1 (
    echo   ✅ All service ports are now free
) else (
    echo   ⚠️  Some ports might still be in use
    echo   Ports still in use:
    netstat -an | findstr ":3000 :3001 :3002 :3003 :3004"
)

echo.
echo 📊 Database containers (still running):
docker ps --format "table {{.Names}}\t{{.Status}}" | findstr mongo
if errorlevel 1 (
    echo   ℹ️  No database containers running
) else (
    echo   ℹ️  Database containers are still running (this is normal)
    echo   Use "docker-compose down" to stop databases if needed
)

echo.
echo ✅ All services stopped successfully!
echo.
echo 🔧 Next actions:
echo   🚀 Start again: scripts\start-all-services.bat
echo   🗄️  Stop databases: docker-compose down
echo   🧹 Clean logs: del logs\*.log

pause
