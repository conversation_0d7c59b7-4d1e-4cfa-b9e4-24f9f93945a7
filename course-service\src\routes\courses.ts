import { Router } from 'express';
import { body, query } from 'express-validator';
import { CourseController } from '../controllers/CourseController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const courseController = new CourseController();

// Create course
router.post('/',
  authenticateToken,
  body('title').trim().isLength({ min: 3, max: 200 }),
  body('description').trim().isLength({ min: 10, max: 2000 }),
  body('category').trim().isLength({ min: 2, max: 100 }),
  body('duration').isInt({ min: 1, max: 365 }),
  body('price').isFloat({ min: 0 }),
  body('currency').optional().isIn(['IDR', 'USD']),
  validateRequest,
  courseController.createCourse
);

// Get all courses
router.get('/',
  authenticateToken,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('category').optional().trim(),
  query('search').optional().trim(),
  query('status').optional().isIn(['draft', 'published', 'archived']),
  validateRequest,
  courseController.getAllCourses
);

// Get course by ID
router.get('/:courseId',
  authenticateToken,
  courseController.getCourseById
);

// Update course
router.put('/:courseId',
  authenticateToken,
  body('title').optional().trim().isLength({ min: 3, max: 200 }),
  body('description').optional().trim().isLength({ min: 10, max: 2000 }),
  body('category').optional().trim().isLength({ min: 2, max: 100 }),
  body('duration').optional().isInt({ min: 1, max: 365 }),
  body('price').optional().isFloat({ min: 0 }),
  validateRequest,
  courseController.updateCourse
);

// Delete course
router.delete('/:courseId',
  authenticateToken,
  courseController.deleteCourse
);

// Publish course
router.post('/:courseId/publish',
  authenticateToken,
  courseController.publishCourse
);

// Get student progress
router.get('/:courseId/progress',
  authenticateToken,
  courseController.getStudentProgress
);

export default router;