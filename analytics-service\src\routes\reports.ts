import { Router } from 'express';
import { ReportController } from '../controllers/ReportController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const reportController = new ReportController();

// Generate user report
router.get('/users',
  authenticateToken,
  reportController.generateUserReport
);

// Generate course report
router.get('/courses',
  authenticateToken,
  reportController.generateCourseReport
);

// Generate revenue report
router.get('/revenue',
  authenticateToken,
  reportController.generateRevenueReport
);

// Generate tutor performance report
router.get('/tutors',
  authenticateToken,
  reportController.generateTutorReport
);

export default router;