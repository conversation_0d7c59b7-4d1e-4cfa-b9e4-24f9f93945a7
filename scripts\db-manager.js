#!/usr/bin/env node

/**
 * Database management script untuk development
 * Jalankan dengan: node scripts/db-manager.js [command]
 */

const mongoose = require('mongoose');
const { execSync } = require('child_process');

// Database URIs
const databases = {
  auth: process.env.AUTH_MONGODB_URI || 'mongodb://localhost:27017/time_course_auth',
  course: process.env.COURSE_MONGODB_URI || 'mongodb://localhost:27017/time_course_courses',
  payment: process.env.PAYMENT_MONGODB_URI || 'mongodb://localhost:27017/time_course_payments',
  analytics: process.env.ANALYTICS_MONGODB_URI || 'mongodb://localhost:27017/time_course_analytics'
};

class DatabaseManager {
  async connectToDatabase(uri) {
    try {
      await mongoose.connect(uri);
      return mongoose.connection.db;
    } catch (error) {
      console.error(`❌ Failed to connect to ${uri}:`, error.message);
      throw error;
    }
  }

  async listCollections(dbName) {
    console.log(`\n📋 Collections in ${dbName}:`);
    console.log('='.repeat(50));
    
    const db = await this.connectToDatabase(databases[dbName]);
    const collections = await db.listCollections().toArray();
    
    if (collections.length === 0) {
      console.log('  No collections found');
      return;
    }

    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`  📁 ${collection.name.padEnd(20)} (${count} documents)`);
    }
    
    await mongoose.disconnect();
  }

  async showUsers() {
    console.log('\n👥 Users in Auth Database:');
    console.log('='.repeat(80));
    
    const db = await this.connectToDatabase(databases.auth);
    const users = await db.collection('users').find({}).toArray();
    
    if (users.length === 0) {
      console.log('  No users found');
      return;
    }

    users.forEach(user => {
      console.log(`  👤 ${user.role?.toUpperCase().padEnd(8)} | ${user.email?.padEnd(25)} | ${user.displayName}`);
    });
    
    await mongoose.disconnect();
  }

  async clearTestData() {
    console.log('🧹 Clearing test data from all databases...');
    
    for (const [dbName, uri] of Object.entries(databases)) {
      try {
        const db = await this.connectToDatabase(uri);
        
        // Clear test users (emails ending with @test.com)
        if (dbName === 'auth') {
          const result = await db.collection('users').deleteMany({ 
            email: { $regex: '@test\.com$' } 
          });
          console.log(`  ✅ ${dbName}: Removed ${result.deletedCount} test users`);
        }
        
        // Clear test courses (created by test tutors)
        if (dbName === 'course') {
          // First get test tutor IDs
          const authDb = await this.connectToDatabase(databases.auth);
          const testTutors = await authDb.collection('users').find({ 
            email: { $regex: '@test\.com$' },
            role: 'tutor'
          }).toArray();
          
          if (testTutors.length > 0) {
            const tutorIds = testTutors.map(t => t._id.toString());
            const result = await db.collection('courses').deleteMany({ 
              tutorId: { $in: tutorIds } 
            });
            console.log(`  ✅ ${dbName}: Removed ${result.deletedCount} test courses`);
          }
          
          await mongoose.disconnect();
          await this.connectToDatabase(uri);
        }
        
        await mongoose.disconnect();
      } catch (error) {
        console.error(`  ❌ Error clearing ${dbName}:`, error.message);
      }
    }
    
    console.log('✅ Test data cleanup completed');
  }

  async resetAllDatabases() {
    console.log('⚠️  WARNING: This will delete ALL data in ALL databases!');
    console.log('Are you sure? This action cannot be undone.');
    
    // In a real scenario, you'd want to add confirmation prompt
    console.log('🧹 Resetting all databases...');
    
    for (const [dbName, uri] of Object.entries(databases)) {
      try {
        const db = await this.connectToDatabase(uri);
        await db.dropDatabase();
        console.log(`  ✅ ${dbName}: Database reset`);
        await mongoose.disconnect();
      } catch (error) {
        console.error(`  ❌ Error resetting ${dbName}:`, error.message);
      }
    }
    
    console.log('✅ All databases reset completed');
  }

  async showStats() {
    console.log('\n📊 Database Statistics:');
    console.log('='.repeat(80));
    
    for (const [dbName, uri] of Object.entries(databases)) {
      try {
        const db = await this.connectToDatabase(uri);
        const stats = await db.stats();
        
        console.log(`\n🗄️  ${dbName.toUpperCase()} Database:`);
        console.log(`   Collections: ${stats.collections}`);
        console.log(`   Documents: ${stats.objects}`);
        console.log(`   Data Size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   Storage Size: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);
        
        await mongoose.disconnect();
      } catch (error) {
        console.error(`  ❌ Error getting stats for ${dbName}:`, error.message);
      }
    }
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];
  const dbManager = new DatabaseManager();
  
  try {
    switch (command) {
      case 'list':
        const dbName = process.argv[3] || 'auth';
        await dbManager.listCollections(dbName);
        break;
        
      case 'users':
        await dbManager.showUsers();
        break;
        
      case 'clear-test':
        await dbManager.clearTestData();
        break;
        
      case 'reset':
        await dbManager.resetAllDatabases();
        break;
        
      case 'stats':
        await dbManager.showStats();
        break;
        
      default:
        console.log('🔧 Database Manager Commands:');
        console.log('  list [db]     - List collections (auth|course|payment|analytics)');
        console.log('  users         - Show all users');
        console.log('  clear-test    - Clear test data only');
        console.log('  reset         - Reset all databases (DANGER!)');
        console.log('  stats         - Show database statistics');
        console.log('\nExample: node scripts/db-manager.js users');
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = DatabaseManager;
