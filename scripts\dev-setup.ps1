# Development setup script untuk Windows PowerShell
# Jalankan dengan: powershell -ExecutionPolicy Bypass -File scripts/dev-setup.ps1 [command]

param(
    [string]$Command = "help"
)

# Colors for output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Blue }

# Check if Docker is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Success "Docker is running"
        return $true
    }
    catch {
        Write-Error "Docker is not running. Please start Docker first."
        return $false
    }
}

# Start only databases
function Start-Databases {
    Write-Info "Starting only MongoDB databases..."
    
    docker-compose up -d mongo-auth mongo-course mongo-payment mongo-analytics
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Databases started successfully"
        Write-Info "Waiting for databases to be ready..."
        Start-Sleep -Seconds 10
    } else {
        Write-Error "Failed to start databases"
        exit 1
    }
}

# Install dependencies for services
function Install-Dependencies {
    $services = @("auth-service", "course-service", "payment-service", "analytics-service", "api-gateway")
    
    foreach ($service in $services) {
        if (!(Test-Path "$service/node_modules")) {
            Write-Info "Installing $service dependencies..."
            Push-Location $service
            npm install
            Pop-Location
        }
    }
}

# Start services in development mode
function Start-DevServices {
    Write-Info "Starting services in development mode..."
    
    Install-Dependencies
    
    # Create logs directory
    if (!(Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs"
    }
    
    Write-Info "Starting services with hot reload..."
    
    # Start services in background
    Start-Process -FilePath "cmd" -ArgumentList "/c", "cd auth-service && npm run dev > ../logs/auth-service.log 2>&1" -WindowStyle Hidden
    Start-Process -FilePath "cmd" -ArgumentList "/c", "cd course-service && npm run dev > ../logs/course-service.log 2>&1" -WindowStyle Hidden
    Start-Process -FilePath "cmd" -ArgumentList "/c", "cd payment-service && npm run dev > ../logs/payment-service.log 2>&1" -WindowStyle Hidden
    Start-Process -FilePath "cmd" -ArgumentList "/c", "cd analytics-service && npm run dev > ../logs/analytics-service.log 2>&1" -WindowStyle Hidden
    Start-Process -FilePath "cmd" -ArgumentList "/c", "cd api-gateway && npm run dev > ../logs/api-gateway.log 2>&1" -WindowStyle Hidden
    
    Write-Success "All services started in development mode"
    Write-Info "Services are running with hot reload enabled"
    Write-Info "Logs are available in logs/ directory"
}

# Setup test data
function Setup-TestData {
    Write-Info "Setting up test data..."
    
    # Wait for services to be ready
    Start-Sleep -Seconds 15
    
    # Create test users
    node scripts/seed-test-data.js
    
    # Generate test tokens
    node scripts/generate-test-tokens.js
    
    Write-Success "Test data setup completed"
}

# Stop development services
function Stop-DevServices {
    Write-Info "Stopping development services..."
    
    # Kill node processes
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.ProcessName -eq "node" } | Stop-Process -Force
    
    Write-Success "Development services stopped"
}

# Show service status
function Show-Status {
    Write-Info "Service Status:"
    Write-Host "===================="
    
    # Check databases
    Write-Host "📊 Databases:"
    docker-compose ps mongo-auth mongo-course mongo-payment mongo-analytics
    
    Write-Host ""
    Write-Host "🚀 Services:"
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Host "  ✅ Node.js processes running: $($nodeProcesses.Count)"
        $nodeProcesses | ForEach-Object { Write-Host "    - PID: $($_.Id)" }
    } else {
        Write-Host "  ❌ No Node.js processes running"
    }
    
    Write-Host ""
    Write-Host "🌐 Endpoints:"
    Write-Host "  API Gateway:       http://localhost:3000"
    Write-Host "  Auth Service:      http://localhost:3001"
    Write-Host "  Course Service:    http://localhost:3002"
    Write-Host "  Payment Service:   http://localhost:3003"
    Write-Host "  Analytics Service: http://localhost:3004"
}

# Show logs
function Show-Logs {
    param($Service)
    
    if (!$Service) {
        Write-Info "Available logs:"
        if (Test-Path "logs") {
            Get-ChildItem "logs" | ForEach-Object { Write-Host "  - $($_.Name)" }
        } else {
            Write-Host "No logs directory found"
        }
        return
    }
    
    $logFile = "logs/$Service.log"
    if (Test-Path $logFile) {
        Get-Content $logFile -Wait
    } else {
        Write-Error "Log file not found: $logFile"
    }
}

# Main command handler
switch ($Command.ToLower()) {
    "start" {
        if (Test-Docker) {
            Start-Databases
            Start-DevServices
            Setup-TestData
            Show-Status
        }
    }
    "stop" {
        Stop-DevServices
        docker-compose down
    }
    "restart" {
        Stop-DevServices
        Start-DevServices
    }
    "status" {
        Show-Status
    }
    "logs" {
        Show-Logs $args[1]
    }
    "db-only" {
        if (Test-Docker) {
            Start-Databases
            Write-Success "Only databases started. Use 'npm run dev' in each service directory for development."
        }
    }
    "test-data" {
        Setup-TestData
    }
    default {
        Write-Host "🔧 Development Setup Commands:"
        Write-Host "  start      - Start databases and all services in dev mode"
        Write-Host "  stop       - Stop all services and databases"
        Write-Host "  restart    - Restart development services only"
        Write-Host "  status     - Show status of all services"
        Write-Host "  logs [svc] - Show logs for specific service"
        Write-Host "  db-only    - Start only databases"
        Write-Host "  test-data  - Setup test data only"
        Write-Host ""
        Write-Host "Examples:"
        Write-Host "  powershell -ExecutionPolicy Bypass -File scripts/dev-setup.ps1 start"
        Write-Host "  powershell -ExecutionPolicy Bypass -File scripts/dev-setup.ps1 status"
    }
}
