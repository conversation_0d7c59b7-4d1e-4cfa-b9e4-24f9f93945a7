import mongoose, { Document, Schema } from "mongoose";
import {
  AssignmentSubmission as IAssignmentSubmission,
  AnswerSubmission,
} from "../../../shared/types";

export interface AssignmentSubmissionDocument
  extends Omit<IAssignmentSubmission, "_id">,
    Document {}

const answerSubmissionSchema = new Schema({
  questionId: { type: String, required: true },
  answer: { type: String, required: true },
  isCorrect: { type: Boolean, required: true },
  points: { type: Number, required: true },
});

const assignmentSubmissionSchema = new Schema<AssignmentSubmissionDocument>(
  {
    studentId: { type: String, required: true, index: true },
    assignmentId: { type: String, required: true },
    lessonId: { type: String, required: true },
    courseId: { type: String, required: true, index: true },
    answers: [answerSubmissionSchema],
    score: { type: Number, required: true },
    maxScore: { type: Number, required: true },
    passed: { type: Boolean, required: true },
    submittedAt: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Indexes
assignmentSubmissionSchema.index({ studentId: 1, courseId: 1 });
assignmentSubmissionSchema.index(
  { assignmentId: 1, studentId: 1 },
  { unique: true }
);

export const AssignmentSubmissionModel =
  mongoose.model<AssignmentSubmissionDocument>(
    "AssignmentSubmission",
    assignmentSubmissionSchema
  );
