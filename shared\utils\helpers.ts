import { ApiResponse } from '../types';

export const createApiResponse = <T>(
  success: boolean,
  message: string,
  data?: T,
  error?: string
): ApiResponse<T> => {
  return {
    success,
    message,
    data,
    error,
    timestamp: new Date()
  };
};

export const calculateProgress = (
  completedModules: string[],
  totalModules: number,
  completedLessons: string[],
  totalLessons: number
): number => {
  const moduleProgress = (completedModules.length / totalModules) * 50;
  const lessonProgress = (completedLessons.length / totalLessons) * 50;
  return Math.round(moduleProgress + lessonProgress);
};

export const calculatePace = (
  startDate: Date,
  totalModules: number,
  completedModules: number
): { expectedModules: number; isOnPace: boolean; daysPassed: number } => {
  const now = new Date();
  const daysPassed = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const expectedModules = Math.floor((daysPassed / 90) * totalModules); // Assuming 90-day course
  
  return {
    expectedModules,
    isOnPace: completedModules >= expectedModules,
    daysPassed
  };
};

export const generateVerificationCode = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `TC-${timestamp}-${random}`.toUpperCase();
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/<script[^>]*>.*?<\/script>/gi, '');
};