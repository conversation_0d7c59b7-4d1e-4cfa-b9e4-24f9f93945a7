export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

export const COURSE_CONSTANTS = {
  MAX_FAILURES: 3,
  DEFAULT_DURATION: 90, // days
  MIN_ASSIGNMENT_SCORE: 100, // percentage
  DEFAULT_TIME_LIMIT: 300 // 5 minutes in seconds
} as const;

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100
} as const;

export const VIDEO_CONFIG = {
  SUPPORTED_FORMATS: ['mp4', 'mov', 'avi'],
  MAX_SIZE: 2 * 1024 * 1024 * 1024, // 2GB
  STREAMING_PROTOCOL: 'HLS'
} as const;

export const PAYMENT_CONFIG = {
  SUPPORTED_CURRENCIES: ['IDR', 'USD'],
  DEFAULT_CURRENCY: 'IDR',
  WEBHOOK_TIMEOUT: 30000 // 30 seconds
} as const;