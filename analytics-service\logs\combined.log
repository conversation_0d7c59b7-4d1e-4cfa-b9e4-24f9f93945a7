{"level":"info","message":"Connected to MongoDB","service":"analytics-service","timestamp":"2025-09-24T07:39:53.374Z"}
{"level":"info","message":"Analytics Service running on port 3004","service":"analytics-service","timestamp":"2025-09-24T07:39:53.379Z"}
{"level":"info","message":"Connected to MongoDB","service":"analytics-service","timestamp":"2025-09-24T07:45:53.733Z"}
{"level":"info","message":"Analytics Service running on port 3004","service":"analytics-service","timestamp":"2025-09-24T07:45:53.737Z"}
{"level":"info","message":"Running analytics data collection...","service":"analytics-service","timestamp":"2025-09-24T08:00:00.884Z"}
{"cause":{"code":"ENOTFOUND","errno":-3008,"hostname":"course-service","syscall":"getaddrinfo"},"code":"ENOTFOUND","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://course-service:3002/api/courses/stats","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-3008,"hostname":"course-service","level":"error","message":"Failed to collect course stats: getaddrinfo ENOTFOUND course-service","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"auth-service:3001:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"auth-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://auth-service:3001/api/users/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"auth-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/users/stats","pathname":"/api/users/stats","port":"3001","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":197,"pendingcb":1,"writelen":197},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://auth-service:3001/api/users/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"auth-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/users/stats","pathname":"/api/users/stats","port":"3001","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}],"course-service:3002:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"course-service","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":201,"pendingcb":1,"writelen":201},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":3},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"course-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/courses/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://course-service:3002/api/courses/stats","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"course-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"auth-service:3001:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"auth-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://auth-service:3001/api/users/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"auth-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/users/stats","pathname":"/api/users/stats","port":"3001","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":197,"pendingcb":1,"writelen":197},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://auth-service:3001/api/users/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"auth-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/users/stats","pathname":"/api/users/stats","port":"3001","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}],"course-service:3002:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"course-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"course-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/courses/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":201,"pendingcb":1,"writelen":201},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"_idleStart":849927,"_idleTimeout":5000,"_repeat":null},"expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":3},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/courses/stats","pathname":"/api/courses/stats","port":"3002","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"service":"analytics-service","stack":"Error: getaddrinfo ENOTFOUND course-service\n    at Function.AxiosError.from (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\core\\AxiosError.js:96:14)\n    at RedirectableRequest.handleRequestError (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\adapters\\http.js:638:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:103:11)\n    at Socket.socketErrorListener (node:_http_client:506:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DataCollectionService.collectCourseStats (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:100:24)\n    at async Promise.all (index 1)\n    at async DataCollectionService.collectAllData (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:16:54)\n    at async Task._execution (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\server.ts:61:3)","syscall":"getaddrinfo","timestamp":"2025-09-24T08:00:03.617Z"}
{"cause":{"code":"ENOTFOUND","errno":-3008,"hostname":"auth-service","syscall":"getaddrinfo"},"code":"ENOTFOUND","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://auth-service:3001/api/users/stats","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-3008,"hostname":"auth-service","level":"error","message":"Failed to collect user stats: getaddrinfo ENOTFOUND auth-service","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"auth-service:3001:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"auth-service","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":197,"pendingcb":1,"writelen":197},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"course-service:3002:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"course-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://course-service:3002/api/courses/stats","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"course-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/courses/stats","pathname":"/api/courses/stats","port":"3002","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"course-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/courses/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":201,"pendingcb":1,"writelen":201},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":"[Circular]","maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":3},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://auth-service:3001/api/users/stats","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"auth-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"auth-service:3001:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"auth-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"auth-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/users/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/users/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: auth-service:3001\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":197,"pendingcb":1,"writelen":197},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"course-service:3002:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"timeout":[null,null,null]},"_eventsCount":8,"_hadError":true,"_host":"course-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://course-service:3002/api/courses/stats","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"course-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/courses/stats","pathname":"/api/courses/stats","port":"3002","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"course-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/courses/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/courses/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: course-service:3002\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":201,"pendingcb":1,"writelen":201},"allowHalfOpen":false,"connecting":false,"parser":null,"server":null,"timeout":5000}],"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"error":[null,null],"response":[null,null],"socket":[null,null]},"_eventsCount":6,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":"[Circular]","path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":{"_destroyed":false,"_idleNext":{"_idleNext":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":"[Circular]","_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idlePrev":{"_destroyed":false,"_idleNext":"[Circular]","_idlePrev":{"_idleNext":"[Circular]","_idlePrev":"[Circular]","expiry":854925,"id":-9007199254739689,"msecs":5000,"priorityQueuePosition":3},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_idleStart":849928,"_idleTimeout":5000,"_repeat":null},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":3},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/users/stats","pathname":"/api/users/stats","port":"3001","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"service":"analytics-service","stack":"Error: getaddrinfo ENOTFOUND auth-service\n    at Function.AxiosError.from (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\core\\AxiosError.js:96:14)\n    at RedirectableRequest.handleRequestError (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\adapters\\http.js:638:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:103:11)\n    at Socket.socketErrorListener (node:_http_client:506:5)\n    at Socket.emit (node:events:518:28)\n    at Axios.request (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DataCollectionService.collectUserStats (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:78:24)\n    at async Promise.all (index 0)\n    at async DataCollectionService.collectAllData (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:16:54)\n    at async Task._execution (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\server.ts:61:3)","syscall":"getaddrinfo","timestamp":"2025-09-24T08:00:03.626Z"}
{"code":"ECONNABORTED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":5000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"http://payment-service:3003/api/subscriptions/stats","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Failed to collect payment stats: timeout of 5000ms exceeded","name":"AxiosError","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":"[Circular]","_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":"[Circular]","server":null,"timeout":5000}},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"http://payment-service:3003/api/subscriptions/stats","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.12.2"},"hostname":"payment-service","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{"payment-service:3003:":[{"_closeAfterHandlingError":false,"_events":{"close":[null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":9,"_hadError":false,"_host":"payment-service","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":"[Circular]","socket":"[Circular]"},"path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_parent":null,"_pendingData":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_pendingEncoding":"latin1","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_server":null,"_sockname":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":208,"pendingcb":1,"writelen":208},"allowHalfOpen":false,"connecting":true,"parser":{"0":null,"5":null,"6":null,"_consumed":false,"_headers":[],"_url":"","incoming":null,"maxHeaderPairs":2000,"outgoing":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/subscriptions/stats HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.12.2\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: payment-service:3003\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"payment-service","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":"[Circular]","path":"/api/subscriptions/stats","protocol":"http:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"socket":"[Circular]"},"server":null,"timeout":5000}]},"totalSocketCount":1},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0}}},"path":"/api/subscriptions/stats","pathname":"/api/subscriptions/stats","port":"3003","protocol":"http:"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"service":"analytics-service","stack":"AxiosError: timeout of 5000ms exceeded\n    at RedirectableRequest.handleRequestTimeout (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\adapters\\http.js:675:16)\n    at RedirectableRequest.emit (node:events:518:28)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at Timeout.<anonymous> (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\follow-redirects\\index.js:221:12)\n    at listOnTimeout (node:internal/timers:594:17)\n    at processTimers (node:internal/timers:529:7)\n    at Axios.request (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:555:9)\n    at processTimers (node:internal/timers:529:7)\n    at async DataCollectionService.collectPaymentStats (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:128:24)\n    at async Promise.all (index 2)\n    at async DataCollectionService.collectAllData (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\services\\DataCollectionService.ts:16:54)\n    at async Task._execution (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-ku7vautg\\timee\\analytics-service\\src\\server.ts:61:3)","timestamp":"2025-09-24T08:00:05.908Z"}
{"level":"info","message":"Analytics data collected and saved successfully","service":"analytics-service","timestamp":"2025-09-24T08:00:05.923Z"}
