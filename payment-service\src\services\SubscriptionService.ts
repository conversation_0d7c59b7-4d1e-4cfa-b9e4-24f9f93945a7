import { SubscriptionModel } from '../models/Subscription';
import { SubscriptionStatus } from '../../../shared/types';
import { logger } from '../utils/logger';

export class SubscriptionService {
  async checkExpiredSubscriptions(): Promise<void> {
    try {
      const now = new Date();
      
      const expiredSubscriptions = await SubscriptionModel.find({
        status: SubscriptionStatus.ACTIVE,
        endDate: { $lt: now }
      });

      if (expiredSubscriptions.length === 0) {
        logger.info('No expired subscriptions found');
        return;
      }

      // Update expired subscriptions
      await SubscriptionModel.updateMany(
        {
          status: SubscriptionStatus.ACTIVE,
          endDate: { $lt: now }
        },
        {
          status: SubscriptionStatus.EXPIRED,
          updatedAt: now
        }
      );

      logger.info(`Expired ${expiredSubscriptions.length} subscriptions`);

      // Optionally notify students about expiration
      for (const subscription of expiredSubscriptions) {
        await this.notifyStudentOfExpiration(subscription.studentId, subscription.courseId);
      }

    } catch (error) {
      logger.error('Check expired subscriptions error:', error);
    }
  }

  async extendSubscription(subscriptionId: string, additionalDays: number): Promise<void> {
    try {
      const subscription = await SubscriptionModel.findById(subscriptionId);

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const newEndDate = new Date(subscription.endDate.getTime() + (additionalDays * 24 * 60 * 60 * 1000));
      
      subscription.endDate = newEndDate;
      if (subscription.status === SubscriptionStatus.EXPIRED) {
        subscription.status = SubscriptionStatus.ACTIVE;
      }

      await subscription.save();

      logger.info(`Subscription extended: ${subscriptionId} by ${additionalDays} days`);

    } catch (error) {
      logger.error('Extend subscription error:', error);
      throw error;
    }
  }

  async getSubscriptionStats(): Promise<any> {
    try {
      const stats = await SubscriptionModel.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const totalRevenue = await SubscriptionModel.aggregate([
        {
          $lookup: {
            from: 'payments',
            localField: 'paymentId',
            foreignField: '_id',
            as: 'payment'
          }
        },
        {
          $unwind: '$payment'
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$payment.amount' }
          }
        }
      ]);

      return {
        subscriptionStats: stats,
        totalRevenue: totalRevenue[0]?.totalRevenue || 0
      };

    } catch (error) {
      logger.error('Get subscription stats error:', error);
      throw error;
    }
  }

  private async notifyStudentOfExpiration(studentId: string, courseId: string): Promise<void> {
    try {
      // Here you would implement notification logic
      // For now, just log the expiration
      logger.info(`Subscription expired for student ${studentId}, course ${courseId}`);
      
      // In a real implementation, you might:
      // - Send email notification
      // - Create in-app notification
      // - Update student dashboard

    } catch (error) {
      logger.error('Notify student of expiration error:', error);
    }
  }
}