#!/usr/bin/env node

/**
 * Script untuk testing API dengan berbagai role
 * Jalankan dengan: node scripts/test-api.js [role] [endpoint]
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

class APITester {
  constructor() {
    this.tokens = this.loadTokens();
  }

  loadTokens() {
    try {
      if (fs.existsSync('test-tokens.json')) {
        return JSON.parse(fs.readFileSync('test-tokens.json', 'utf8'));
      }
    } catch (error) {
      console.error('❌ Error loading tokens:', error.message);
    }
    return {};
  }

  getToken(role) {
    if (!this.tokens[role] || this.tokens[role].length === 0) {
      throw new Error(`No token found for role: ${role}. Run 'npm run gen-tokens' first.`);
    }
    return this.tokens[role][0].token;
  }

  async makeRequest(method, endpoint, data = null, token = null) {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return {
        success: true,
        status: response.status,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        status: error.response?.status || 0,
        data: error.response?.data || { message: error.message }
      };
    }
  }

  async testEndpoint(role, method, endpoint, data = null) {
    console.log(`\n🧪 Testing ${method.toUpperCase()} ${endpoint} as ${role.toUpperCase()}`);
    console.log('='.repeat(80));

    try {
      const token = this.getToken(role);
      const result = await this.makeRequest(method, endpoint, data, token);

      if (result.success) {
        console.log(`✅ SUCCESS (${result.status})`);
        console.log('Response:', JSON.stringify(result.data, null, 2));
      } else {
        console.log(`❌ FAILED (${result.status})`);
        console.log('Error:', JSON.stringify(result.data, null, 2));
      }

      return result;
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async runTestSuite() {
    console.log('🚀 Running API Test Suite');
    console.log('='.repeat(80));

    const tests = [
      // Auth endpoints
      { role: 'admin', method: 'GET', endpoint: '/api/auth/profile', description: 'Admin profile' },
      { role: 'tutor', method: 'GET', endpoint: '/api/auth/profile', description: 'Tutor profile' },
      { role: 'student', method: 'GET', endpoint: '/api/auth/profile', description: 'Student profile' },

      // User management (Admin only)
      { role: 'admin', method: 'GET', endpoint: '/api/users', description: 'Get all users (Admin)' },
      { role: 'tutor', method: 'GET', endpoint: '/api/users', description: 'Get all users (Tutor - should fail)' },
      { role: 'student', method: 'GET', endpoint: '/api/users', description: 'Get all users (Student - should fail)' },

      // Course management
      { 
        role: 'tutor', 
        method: 'POST', 
        endpoint: '/api/courses',
        data: {
          title: 'Test Course by Tutor',
          description: 'A test course created by tutor',
          category: 'English',
          duration: 30,
          price: 100000,
          currency: 'IDR'
        },
        description: 'Create course (Tutor)'
      },
      { 
        role: 'student', 
        method: 'POST', 
        endpoint: '/api/courses',
        data: {
          title: 'Test Course by Student',
          description: 'This should fail',
          category: 'English',
          duration: 30,
          price: 100000
        },
        description: 'Create course (Student - should fail)'
      },

      // Get courses (should work for all)
      { role: 'admin', method: 'GET', endpoint: '/api/courses', description: 'Get courses (Admin)' },
      { role: 'tutor', method: 'GET', endpoint: '/api/courses', description: 'Get courses (Tutor)' },
      { role: 'student', method: 'GET', endpoint: '/api/courses', description: 'Get courses (Student)' }
    ];

    const results = [];

    for (const test of tests) {
      const result = await this.testEndpoint(
        test.role, 
        test.method, 
        test.endpoint, 
        test.data
      );
      
      results.push({
        ...test,
        result: result.success,
        status: result.status
      });

      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Summary
    console.log('\n📊 Test Results Summary:');
    console.log('='.repeat(80));

    const passed = results.filter(r => r.result).length;
    const failed = results.filter(r => !r.result).length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📊 Total: ${results.length}`);

    console.log('\n📋 Detailed Results:');
    results.forEach(test => {
      const status = test.result ? '✅' : '❌';
      console.log(`  ${status} ${test.role.toUpperCase().padEnd(8)} ${test.method.padEnd(4)} ${test.endpoint.padEnd(20)} (${test.status})`);
    });

    return results;
  }

  async testSpecificEndpoint(role, endpoint) {
    // Determine method based on endpoint
    let method = 'GET';
    let data = null;

    if (endpoint.includes('/api/courses') && !endpoint.includes('?')) {
      method = 'POST';
      data = {
        title: `Test Course by ${role}`,
        description: 'A test course',
        category: 'English',
        duration: 30,
        price: 100000,
        currency: 'IDR'
      };
    }

    return await this.testEndpoint(role, method, endpoint, data);
  }
}

// CLI interface
async function main() {
  const tester = new APITester();
  const role = process.argv[2];
  const endpoint = process.argv[3];

  try {
    if (!role) {
      // Run full test suite
      await tester.runTestSuite();
    } else if (!endpoint) {
      console.log(`❌ Please provide an endpoint to test for role: ${role}`);
      console.log('Example: node scripts/test-api.js admin /api/users');
    } else {
      await tester.testSpecificEndpoint(role, endpoint);
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = APITester;
