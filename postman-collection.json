{"info": {"name": "Time Course Platform API", "description": "Complete API collection for Time Course Platform with authentication and all endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "admin_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.EY9B3fdgEJCvYjcKrk6QRGhopITOb66mt3nRQoadLLU", "type": "string"}, {"key": "tutor_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.Xsk_7KtYD9hf7IM63kmQc5-QJF4oKFTuw8AriZXgO5k", "type": "string"}, {"key": "student_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************.O-2vd1RZf4xw5F0WRs1mSEmn3x7m6AsLqO0IBUXKk_g", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API Gateway health status"}}, {"name": "Authentication", "item": [{"name": "Firebase Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"idToken\": \"your-firebase-id-token-here\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/firebase-login", "host": ["{{base_url}}"], "path": ["api", "auth", "firebase-login"]}, "description": "Login with Firebase ID token"}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/auth/profile", "host": ["{{base_url}}"], "path": ["api", "auth", "profile"]}, "description": "Get current user profile"}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"Updated Name\",\n  \"photoURL\": \"https://example.com/photo.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/profile", "host": ["{{base_url}}"], "path": ["api", "auth", "profile"]}, "description": "Update user profile"}}, {"name": "Create <PERSON><PERSON> (Admin Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"displayName\": \"New Tutor\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/create-tutor", "host": ["{{base_url}}"], "path": ["api", "auth", "create-tutor"]}, "description": "Create new tutor account (Admin only)"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}, "description": "Logout and invalidate token"}}]}, {"name": "User Management", "item": [{"name": "Get All Users (Admin Only)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/users?page=1&limit=10&role=student&status=active", "host": ["{{base_url}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "student"}, {"key": "status", "value": "active"}]}, "description": "Get all users with pagination and filters"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/users/68d39dba545d3c08136cb8de", "host": ["{{base_url}}"], "path": ["api", "users", "68d39dba545d3c08136cb8de"]}, "description": "Get specific user by ID"}}, {"name": "Delete User (Admin Only)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/users/68d39dba545d3c08136cb8de", "host": ["{{base_url}}"], "path": ["api", "users", "68d39dba545d3c08136cb8de"]}, "description": "Delete user (Admin only)"}}]}, {"name": "Course Management", "item": [{"name": "Create Course (Tutor/Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Advanced English Grammar\",\n  \"description\": \"Comprehensive course covering advanced English grammar topics\",\n  \"category\": \"English\",\n  \"duration\": 60,\n  \"price\": 150000,\n  \"currency\": \"IDR\"\n}"}, "url": {"raw": "{{base_url}}/api/courses", "host": ["{{base_url}}"], "path": ["api", "courses"]}, "description": "Create new course (<PERSON><PERSON> or <PERSON><PERSON> only)"}}, {"name": "Get All Courses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/courses?page=1&limit=10&category=English&status=published", "host": ["{{base_url}}"], "path": ["api", "courses"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "English"}, {"key": "status", "value": "published"}]}, "description": "Get all courses with pagination and filters"}}, {"name": "Get Course by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/courses/course-id-here", "host": ["{{base_url}}"], "path": ["api", "courses", "course-id-here"]}, "description": "Get specific course by ID"}}, {"name": "Update Course", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Course Title\",\n  \"description\": \"Updated course description\",\n  \"price\": 200000\n}"}, "url": {"raw": "{{base_url}}/api/courses/course-id-here", "host": ["{{base_url}}"], "path": ["api", "courses", "course-id-here"]}, "description": "Update course details"}}, {"name": "Delete Course", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/api/courses/course-id-here", "host": ["{{base_url}}"], "path": ["api", "courses", "course-id-here"]}, "description": "Delete course"}}, {"name": "Publish Course", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/api/courses/course-id-here/publish", "host": ["{{base_url}}"], "path": ["api", "courses", "course-id-here", "publish"]}, "description": "Publish course"}}, {"name": "Get Student Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/courses/course-id-here/progress", "host": ["{{base_url}}"], "path": ["api", "courses", "course-id-here", "progress"]}, "description": "Get student progress for a course"}}]}, {"name": "Lessons & Learning", "item": [{"name": "Get Lesson Video URL", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/lessons/course-id/modules/module-id/lessons/lesson-id/video", "host": ["{{base_url}}"], "path": ["api", "lessons", "course-id", "modules", "module-id", "lessons", "lesson-id", "video"]}, "description": "Get video URL for a specific lesson"}}, {"name": "Get Lesson Assignments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/lessons/course-id/modules/module-id/lessons/lesson-id/assignments?timestamp=120", "host": ["{{base_url}}"], "path": ["api", "lessons", "course-id", "modules", "module-id", "lessons", "lesson-id", "assignments"], "query": [{"key": "timestamp", "value": "120"}]}, "description": "Get assignments at specific timestamp"}}, {"name": "Update Video Progress", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentTime\": 300,\n  \"completed\": false\n}"}, "url": {"raw": "{{base_url}}/api/lessons/course-id/modules/module-id/lessons/lesson-id/progress", "host": ["{{base_url}}"], "path": ["api", "lessons", "course-id", "modules", "module-id", "lessons", "lesson-id", "progress"]}, "description": "Update video progress for a lesson"}}]}, {"name": "Payments", "item": [{"name": "Create Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"course-id-here\",\n  \"amount\": 150000,\n  \"currency\": \"IDR\"\n}"}, "url": {"raw": "{{base_url}}/api/payments", "host": ["{{base_url}}"], "path": ["api", "payments"]}, "description": "Create payment for course"}}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/payments/payment-id-here", "host": ["{{base_url}}"], "path": ["api", "payments", "payment-id-here"]}, "description": "Get payment status by ID"}}, {"name": "Get Student Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/payments/student/me", "host": ["{{base_url}}"], "path": ["api", "payments", "student", "me"]}, "description": "Get all payments for current student"}}]}, {"name": "Subscriptions", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"course-id-here\",\n  \"paymentId\": \"payment-id-here\",\n  \"duration\": 30\n}"}, "url": {"raw": "{{base_url}}/api/subscriptions", "host": ["{{base_url}}"], "path": ["api", "subscriptions"]}, "description": "Create subscription after successful payment"}}, {"name": "Get Student Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/student/68d39dba545d3c08136cb8de", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "student", "68d39dba545d3c08136cb8de"]}, "description": "Get all subscriptions for a student"}}, {"name": "Check Subscription Access", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/access/course-id-here", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "access", "course-id-here"]}, "description": "Check if student has access to a course"}}, {"name": "Get Terminated Students", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/terminated?courseId=course-id-here", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "terminated"], "query": [{"key": "courseId", "value": "course-id-here"}]}, "description": "Get students who need coaching (terminated subscriptions)"}}]}, {"name": "Analytics", "item": [{"name": "Get Overview Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/overview", "host": ["{{base_url}}"], "path": ["api", "analytics", "overview"]}, "description": "Get overview analytics (Admin/Tutor only)"}}, {"name": "Get Analytics by Date Range", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/range?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.999Z", "host": ["{{base_url}}"], "path": ["api", "analytics", "range"], "query": [{"key": "startDate", "value": "2024-01-01T00:00:00.000Z"}, {"key": "endDate", "value": "2024-12-31T23:59:59.999Z"}]}, "description": "Get analytics for specific date range"}}, {"name": "Get Tutor Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/tutor/68d39dba545d3c08136cb8dc", "host": ["{{base_url}}"], "path": ["api", "analytics", "tutor", "68d39dba545d3c08136cb8dc"]}, "description": "Get analytics for specific tutor"}}, {"name": "Refresh Analytics", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/refresh", "host": ["{{base_url}}"], "path": ["api", "analytics", "refresh"]}, "description": "Manually trigger analytics refresh"}}]}, {"name": "Reports", "item": [{"name": "Generate User Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/reports/users", "host": ["{{base_url}}"], "path": ["api", "reports", "users"]}, "description": "Generate user report (Admin only)"}}, {"name": "Generate Course Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/reports/courses", "host": ["{{base_url}}"], "path": ["api", "reports", "courses"]}, "description": "Generate course report (Admin only)"}}, {"name": "Generate Revenue Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/reports/revenue", "host": ["{{base_url}}"], "path": ["api", "reports", "revenue"]}, "description": "Generate revenue report (Admin only)"}}, {"name": "Generate Tutor Performance Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/reports/tutors", "host": ["{{base_url}}"], "path": ["api", "reports", "tutors"]}, "description": "Generate tutor performance report (Admin only)"}}]}, {"name": "Coaching", "item": [{"name": "Create Coaching Session (Tutor)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"course-id-here\",\n  \"topic\": \"Advanced Grammar Review Session\",\n  \"date\": \"2024-12-01T10:00:00.000Z\",\n  \"meetingLink\": \"https://meet.google.com/abc-defg-hij\",\n  \"maxStudents\": 10\n}"}, "url": {"raw": "{{base_url}}/api/coaching", "host": ["{{base_url}}"], "path": ["api", "coaching"]}, "description": "Create coaching session (Tu<PERSON> only)"}}, {"name": "Get Coaching Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/api/coaching?courseId=course-id-here&status=scheduled", "host": ["{{base_url}}"], "path": ["api", "coaching"], "query": [{"key": "courseId", "value": "course-id-here"}, {"key": "status", "value": "scheduled"}]}, "description": "Get coaching sessions with filters"}}, {"name": "Get Available Coaching Sessions (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/coaching/available", "host": ["{{base_url}}"], "path": ["api", "coaching", "available"]}, "description": "Get available coaching sessions for students"}}, {"name": "Enroll in Coaching Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/coaching/session-id-here/enroll", "host": ["{{base_url}}"], "path": ["api", "coaching", "session-id-here", "enroll"]}, "description": "Enroll student in coaching session"}}]}, {"name": "Certificates", "item": [{"name": "Generate Certificate", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/certificates/generate/course-id-here", "host": ["{{base_url}}"], "path": ["api", "certificates", "generate", "course-id-here"]}, "description": "Generate certificate for completed course"}}, {"name": "Verify Certificate (Public)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/certificates/verify/verification-code-here", "host": ["{{base_url}}"], "path": ["api", "certificates", "verify", "verification-code-here"]}, "description": "Verify certificate by verification code (public endpoint)"}}, {"name": "Get Student Certificates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}], "url": {"raw": "{{base_url}}/api/certificates/student/me", "host": ["{{base_url}}"], "path": ["api", "certificates", "student", "me"]}, "description": "Get all certificates for current student"}}]}, {"name": "<PERSON><PERSON>oks (External)", "item": [{"name": "Xendit Payment Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-callback-token", "value": "your-xendit-callback-token"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"payment-id\",\n  \"external_id\": \"external-payment-id\",\n  \"status\": \"PAID\",\n  \"amount\": 150000,\n  \"currency\": \"IDR\",\n  \"payment_method\": \"BANK_TRANSFER\",\n  \"created\": \"2024-01-01T10:00:00.000Z\",\n  \"updated\": \"2024-01-01T10:05:00.000Z\"\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/xendit/payment", "host": ["{{base_url}}"], "path": ["api", "webhooks", "xendit", "payment"]}, "description": "Xendit payment webhook (called by Xendit)"}}, {"name": "Xendit Invoice Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-callback-token", "value": "your-xendit-callback-token"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"invoice-id\",\n  \"external_id\": \"external-invoice-id\",\n  \"status\": \"PAID\",\n  \"amount\": 150000,\n  \"currency\": \"IDR\",\n  \"created\": \"2024-01-01T10:00:00.000Z\",\n  \"updated\": \"2024-01-01T10:05:00.000Z\"\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/xendit/invoice", "host": ["{{base_url}}"], "path": ["api", "webhooks", "xendit", "invoice"]}, "description": "Xendit invoice webhook (called by Xendit)"}}]}]}