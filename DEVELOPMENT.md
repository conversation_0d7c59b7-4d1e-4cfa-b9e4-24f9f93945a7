# 🚀 Development Guide

Panduan lengkap untuk development yang efisien tanpa perlu rebuild container terus-menerus.

## 🎯 Quick Start untuk Testing

### 1. Setup Development Environment

```bash
# Install dependencies untuk scripts
npm install

# Start databases dan services dengan hot reload
npm run dev:fast

# Atau hanya start databases saja
npm run dev:db-only
```

### 2. Setup Test Data

```bash
# Buat test users dengan berbagai role
npm run seed-test

# Generate JWT tokens untuk testing
npm run gen-tokens
```

### 3. Test API dengan Berbagai Role

```bash
# Test semua endpoint
node scripts/test-api.js

# Test endpoint specific dengan role tertentu
node scripts/test-api.js admin /api/users
node scripts/test-api.js tutor /api/courses
node scripts/test-api.js student /api/auth/profile
```

## 👥 Test Users yang Tersedia

Setelah menjalankan `npm run seed-test`, Anda akan memiliki:

| Role | Email | Password | Deskripsi |
|------|-------|----------|-----------|
| **ADMIN** | <EMAIL> | - | Full access ke semua endpoint |
| **TUTOR** | <EMAIL> | - | Bisa create/manage courses |
| **TUTOR** | <EMAIL> | - | Tutor kedua untuk testing |
| **STUDENT** | <EMAIL> | - | Student aktif |
| **STUDENT** | <EMAIL> | - | Student aktif |
| **STUDENT** | <EMAIL> | - | Student suspended |

## 🔑 Cara Menggunakan JWT Tokens

### 1. Generate Tokens
```bash
npm run gen-tokens
```

### 2. Gunakan Token dalam Request
```bash
# Contoh dengan cURL
curl -X GET "http://localhost:3000/api/users" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. Token tersimpan di `test-tokens.json`
```json
{
  "admin": [
    {
      "email": "<EMAIL>",
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "userId": "..."
    }
  ],
  "tutor": [...],
  "student": [...]
}
```

## 🛠️ Development Commands

### Service Management
```bash
# Start development (databases + services dengan hot reload)
npm run dev:fast

# Start hanya databases
npm run dev:db-only

# Stop semua services
npm run dev:stop

# Restart services (tanpa databases)
npm run dev:restart

# Check status
npm run dev:status

# Lihat logs
./scripts/dev-setup.sh logs auth-service
./scripts/dev-setup.sh logs course-service
```

### Database Management
```bash
# Lihat semua users
npm run db:users

# Lihat collections di database
npm run db:list auth
npm run db:list course

# Lihat statistik database
npm run db:stats

# Hapus hanya test data
npm run db:clear-test
```

### Testing
```bash
# Test semua endpoint dengan semua role
node scripts/test-api.js

# Test endpoint specific
node scripts/test-api.js admin /api/users
node scripts/test-api.js tutor /api/courses
node scripts/test-api.js student /api/auth/profile
```

## 🔍 Cara Melihat Data

### 1. Via Database Manager Script
```bash
# Lihat semua users
npm run db:users

# Lihat collections
npm run db:list auth
```

### 2. Via MongoDB Compass
- Connect ke: `mongodb://localhost:27017`
- Databases:
  - `time_course_auth` - User data
  - `time_course_courses` - Course data
  - `time_course_payments` - Payment data
  - `time_course_analytics` - Analytics data

### 3. Via API Endpoints
```bash
# Get all users (admin only)
curl -X GET "http://localhost:3000/api/users" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Get user profile
curl -X GET "http://localhost:3000/api/auth/profile" \
  -H "Authorization: Bearer USER_TOKEN"

# Get all courses
curl -X GET "http://localhost:3000/api/courses" \
  -H "Authorization: Bearer ANY_TOKEN"
```

## 🚀 Workflow Development yang Efisien

### 1. First Time Setup
```bash
# Clone dan setup
git clone <repo>
cd timee

# Setup environment
cp .env.example .env
# Edit .env dengan credentials yang benar

# Start development
npm run dev:fast
```

### 2. Daily Development
```bash
# Start hanya databases (jika sudah ada)
npm run dev:db-only

# Kemudian jalankan service yang sedang dikerjakan
cd auth-service && npm run dev
# atau
cd course-service && npm run dev
```

### 3. Testing Changes
```bash
# Generate fresh test data
npm run seed-test
npm run gen-tokens

# Test API
node scripts/test-api.js

# Test specific endpoint
node scripts/test-api.js tutor /api/courses
```

## 🎯 Testing Scenarios

### Admin Testing
```bash
# Test admin endpoints
node scripts/test-api.js admin /api/users
node scripts/test-api.js admin /api/auth/create-tutor
```

### Tutor Testing
```bash
# Test tutor endpoints
node scripts/test-api.js tutor /api/courses
node scripts/test-api.js tutor /api/lessons
```

### Student Testing
```bash
# Test student endpoints
node scripts/test-api.js student /api/courses
node scripts/test-api.js student /api/progress
```

### Cross-Role Testing
```bash
# Test yang seharusnya gagal
node scripts/test-api.js student /api/users  # Should fail
node scripts/test-api.js tutor /api/users    # Should fail
```

## 🐛 Troubleshooting

### Services tidak start
```bash
# Check status
npm run dev:status

# Check logs
./scripts/dev-setup.sh logs auth-service

# Restart services
npm run dev:restart
```

### Database connection error
```bash
# Check databases
docker ps | grep mongo

# Restart databases
docker-compose restart mongo-auth mongo-course mongo-payment mongo-analytics
```

### Token expired
```bash
# Generate new tokens
npm run gen-tokens
```

### Clear test data
```bash
# Hapus hanya test data
npm run db:clear-test

# Atau reset semua (HATI-HATI!)
node scripts/db-manager.js reset
```

## 📊 Monitoring

### Check Service Health
```bash
# Via status command
npm run dev:status

# Via HTTP health checks
curl http://localhost:3001/health  # Auth service
curl http://localhost:3002/health  # Course service
curl http://localhost:3003/health  # Payment service
curl http://localhost:3004/health  # Analytics service
```

### View Logs
```bash
# Real-time logs
./scripts/dev-setup.sh logs auth-service

# All logs
tail -f logs/*.log
```

Dengan setup ini, Anda bisa develop dengan efisien tanpa perlu rebuild container terus-menerus! 🎉
