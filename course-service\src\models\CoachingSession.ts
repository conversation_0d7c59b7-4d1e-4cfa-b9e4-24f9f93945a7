import mongoose, { Document, Schema } from "mongoose";
import {
  CoachingSession as ICoachingSession,
  CoachingSessionStatus,
} from "../../../shared/types";

export interface CoachingSessionDocument
  extends Omit<ICoachingSession, "_id">,
    Document {}

const coachingSessionSchema = new Schema<CoachingSessionDocument>(
  {
    tutorId: { type: String, required: true, index: true },
    courseId: { type: String, required: true, index: true },
    topic: { type: String, required: true },
    date: { type: Date, required: true },
    meetingLink: { type: String, required: true },
    maxStudents: { type: Number, required: true, default: 20 },
    enrolledStudents: [{ type: String }],
    status: {
      type: String,
      enum: Object.values(CoachingSessionStatus),
      default: CoachingSessionStatus.SCHEDULED,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Indexes
coachingSessionSchema.index({ tutorId: 1, date: 1 });
coachingSessionSchema.index({ courseId: 1, status: 1 });

export const CoachingSessionModel = mongoose.model<CoachingSessionDocument>(
  "CoachingSession",
  coachingSessionSchema
);
