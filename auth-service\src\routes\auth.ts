import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from '../controllers/AuthController';
import { validateRequest } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const authController = new AuthController();

// Firebase token verification and user creation/login
router.post('/firebase-login',
  body('idToken').notEmpty().withMessage('Firebase ID token is required'),
  validateRequest,
  authController.firebaseLogin
);

// Get current user profile
router.get('/profile',
  authenticateToken,
  authController.getProfile
);

// Update user profile
router.put('/profile',
  authenticateToken,
  body('displayName').optional().trim().isLength({ min: 2, max: 100 }),
  body('photoURL').optional().isURL(),
  validateRequest,
  authController.updateProfile
);

// Admin only: Create tutor account
router.post('/create-tutor',
  authenticateToken,
  body('email').isEmail().normalizeEmail(),
  body('displayName').trim().isLength({ min: 2, max: 100 }),
  validateRequest,
  authController.createTutor
);

// Admin only: Manage user status
router.put('/users/:userId/status',
  authenticateToken,
  body('status').isIn(['active', 'inactive', 'suspended']),
  validateRequest,
  authController.updateUserStatus
);

// Logout (invalidate token)
router.post('/logout',
  authenticateToken,
  authController.logout
);

export default router;