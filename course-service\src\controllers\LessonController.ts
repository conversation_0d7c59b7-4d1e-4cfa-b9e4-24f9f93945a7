import { Request, Response } from 'express';
import { CourseModel } from '../models/Course';
import { UserRole } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import { BunnyService } from '../services/BunnyService';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class LessonController {
  private bunnyService = new BunnyService();

  async getLessonVideoUrl(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, moduleId, lessonId } = req.params;

      // Verify student has access to this course (check subscription in payment service)
      // For now, we'll implement basic access check
      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Course not found')
        );
      }

      const module = course.modules.id(moduleId);
      if (!module) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Module not found')
        );
      }

      const lesson = module.lessons.id(lessonId);
      if (!lesson) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Lesson not found')
        );
      }

      if (!lesson.videoId) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Video not available for this lesson')
        );
      }

      // Generate secure streaming URL with expiration
      const streamingUrl = await this.bunnyService.getSecureStreamingUrl(lesson.videoId);

      res.json(createApiResponse(true, 'Video URL retrieved successfully', {
        streamingUrl,
        duration: lesson.duration,
        assignments: lesson.assignments.map(assignment => ({
          _id: assignment._id,
          title: assignment.title,
          triggerTimestamp: assignment.triggerTimestamp,
          timeLimit: assignment.timeLimit,
          questionCount: assignment.questions.length
        }))
      }));

    } catch (error) {
      logger.error('Get lesson video URL error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve video URL', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getLessonAssignments(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, moduleId, lessonId } = req.params;
      const { timestamp } = req.query;

      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Course not found')
        );
      }

      const module = course.modules.id(moduleId);
      if (!module) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Module not found')
        );
      }

      const lesson = module.lessons.id(lessonId);
      if (!lesson) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Lesson not found')
        );
      }

      // Find assignments that should trigger at this timestamp
      const currentTimestamp = parseInt(timestamp as string) || 0;
      const activeAssignments = lesson.assignments.filter(assignment => 
        Math.abs(assignment.triggerTimestamp - currentTimestamp) <= 2 // 2 second tolerance
      );

      if (activeAssignments.length === 0) {
        return res.json(createApiResponse(true, 'No assignments at this timestamp', { assignments: [] }));
      }

      // Return assignments without correct answers
      const assignmentsData = activeAssignments.map(assignment => ({
        _id: assignment._id,
        title: assignment.title,
        triggerTimestamp: assignment.triggerTimestamp,
        timeLimit: assignment.timeLimit,
        questions: assignment.questions.map(question => ({
          _id: question._id,
          question: question.question,
          type: question.type,
          points: question.points
        }))
      }));

      res.json(createApiResponse(true, 'Assignments retrieved successfully', { assignments: assignmentsData }));

    } catch (error) {
      logger.error('Get lesson assignments error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve assignments', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async updateVideoProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, moduleId, lessonId } = req.params;
      const { currentTime, completed } = req.body;

      // Here you would update the student's video progress
      // This could be stored in a separate VideoProgress model
      // For now, we'll just acknowledge the progress update

      logger.info(`Video progress updated for student ${req.user?.userId}: Course ${courseId}, Lesson ${lessonId}, Time: ${currentTime}`);

      res.json(createApiResponse(true, 'Video progress updated successfully', {
        courseId,
        lessonId,
        currentTime,
        completed
      }));

    } catch (error) {
      logger.error('Update video progress error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to update video progress', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }
}