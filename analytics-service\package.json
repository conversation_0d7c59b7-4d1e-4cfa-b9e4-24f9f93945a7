{"name": "time-course-analytics-service", "version": "1.0.0", "description": "Analytics and Reporting Service", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"axios": "^1.5.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.2.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "node-cron": "^3.0.2", "winston": "^3.10.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.0", "@types/node-cron": "^3.0.8", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}