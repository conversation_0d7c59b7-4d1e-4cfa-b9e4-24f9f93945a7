import { Router } from 'express';
import { query, body } from 'express-validator';
import { LessonController } from '../controllers/LessonController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const lessonController = new LessonController();

// Get lesson video URL
router.get('/:courseId/modules/:moduleId/lessons/:lessonId/video',
  authenticateToken,
  lessonController.getLessonVideoUrl
);

// Get lesson assignments at timestamp
router.get('/:courseId/modules/:moduleId/lessons/:lessonId/assignments',
  authenticateToken,
  query('timestamp').isInt({ min: 0 }),
  validateRequest,
  lessonController.getLessonAssignments
);

// Update video progress
router.post('/:courseId/modules/:moduleId/lessons/:lessonId/progress',
  authenticateToken,
  body('currentTime').isInt({ min: 0 }),
  body('completed').optional().isBoolean(),
  validateRequest,
  lessonController.updateVideoProgress
);

export default router;