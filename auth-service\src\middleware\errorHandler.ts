import { Request, Response, NextFunction } from "express";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS } from "../../../shared/utils/constants";
import { logger } from "../utils/logger";

export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error("Unhandled error:", error);

  // MongoDB duplicate key error
  if (error.code === 11000) {
    return res
      .status(HTTP_STATUS.CONFLICT)
      .json(
        createApiResponse(
          false,
          "Duplicate entry",
          undefined,
          "Resource already exists"
        )
      );
  }

  // JWT errors
  if (error.name === "JsonWebTokenError") {
    return res
      .status(HTTP_STATUS.UNAUTHORIZED)
      .json(createApiResponse(false, "Invalid token"));
  }

  if (error.name === "TokenExpiredError") {
    return res
      .status(HTTP_STATUS.UNAUTHORIZED)
      .json(createApiResponse(false, "Token expired"));
  }

  // Validation errors
  if (error.name === "ValidationError") {
    return res
      .status(HTTP_STATUS.BAD_REQUEST)
      .json(
        createApiResponse(false, "Validation failed", undefined, error.message)
      );
  }

  // Default error
  res
    .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
    .json(
      createApiResponse(
        false,
        "Internal server error",
        undefined,
        "Something went wrong"
      )
    );
};
