@echo off
REM Script untuk check status semua services
REM Jalankan dengan: scripts\check-services.bat

echo 🔍 Checking Service Status
echo ==========================

echo.
echo 🚀 Node.js Processes:
tasklist /fi "imagename eq node.exe" /fo table 2>nul | findstr node.exe
if errorlevel 1 (
    echo   ❌ No Node.js processes running
) else (
    echo   ✅ Node.js processes found
)

echo.
echo 🌐 Port Status:
echo Checking if services are listening on their ports...

REM Check each port
netstat -an | findstr ":3000 " >nul
if errorlevel 1 (
    echo   ❌ Port 3000 (API Gateway) - Not listening
) else (
    echo   ✅ Port 3000 (API Gateway) - Listening
)

netstat -an | findstr ":3001 " >nul
if errorlevel 1 (
    echo   ❌ Port 3001 (Auth Service) - Not listening
) else (
    echo   ✅ Port 3001 (Auth Service) - Listening
)

netstat -an | findstr ":3002 " >nul
if errorlevel 1 (
    echo   ❌ Port 3002 (Course Service) - Not listening
) else (
    echo   ✅ Port 3002 (Course Service) - Listening
)

netstat -an | findstr ":3003 " >nul
if errorlevel 1 (
    echo   ❌ Port 3003 (Payment Service) - Not listening
) else (
    echo   ✅ Port 3003 (Payment Service) - Listening
)

netstat -an | findstr ":3004 " >nul
if errorlevel 1 (
    echo   ❌ Port 3004 (Analytics Service) - Not listening
) else (
    echo   ✅ Port 3004 (Analytics Service) - Listening
)

echo.
echo 📊 Database Status:
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | findstr mongo
if errorlevel 1 (
    echo   ❌ No MongoDB containers running
) else (
    echo   ✅ MongoDB containers running
)

echo.
echo 📁 Recent Log Files:
if exist "logs" (
    dir logs\*.log /o-d /b 2>nul | head -5
) else (
    echo   ❌ No logs directory found
)

echo.
echo 🔧 Quick Actions:
echo   📋 View logs: type "logs\[service-name].log"
echo   🔄 Restart: scripts\start-all-services.bat
echo   🛑 Stop all: scripts\stop-all-services.bat
echo   🧪 Test API: npm run test-api

pause
