import axios from "axios";
import { AnalyticsSnapshotModel } from "../models/AnalyticsSnapshot";
import { Analytics, PopularCourse, TutorStats } from "../../../shared/types";
import { logger } from "../utils/logger";

export class DataCollectionService {
  private authServiceUrl =
    process.env.AUTH_SERVICE_URL || "http://auth-service:3001";
  private courseServiceUrl =
    process.env.COURSE_SERVICE_URL || "http://course-service:3002";
  private paymentServiceUrl =
    process.env.PAYMENT_SERVICE_URL || "http://payment-service:3003";

  async collectAllData(): Promise<void> {
    try {
      const [userStats, courseStats, paymentStats] = await Promise.all([
        this.collectUserStats(),
        this.collectCourseStats(),
        this.collectPaymentStats(),
      ]);

      const analytics: Analytics = {
        totalUsers: userStats.totalUsers,
        activeSubscriptions: paymentStats.activeSubscriptions,
        totalRevenue: paymentStats.totalRevenue,
        popularCourses: courseStats.popularCourses,
        failureRate: paymentStats.failureRate,
        completionRate: courseStats.completionRate,
        tutorStats: courseStats.tutorStats,
      };

      // Save snapshot
      const snapshot = new AnalyticsSnapshotModel({
        date: new Date(),
        ...analytics,
      });

      await snapshot.save();

      logger.info("Analytics data collected and saved successfully");
    } catch (error) {
      logger.error("Data collection error:", error);
    }
  }

  async generateDailyReports(): Promise<void> {
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      const snapshot = await AnalyticsSnapshotModel.findOne({
        date: { $gte: yesterday },
      }).sort({ date: -1 });

      if (!snapshot) {
        logger.warn("No analytics data found for daily report generation");
        return;
      }

      // Here you would generate and send reports
      // For now, just log the summary
      logger.info("Daily Report Summary:", {
        date: snapshot.date,
        totalUsers: snapshot.totalUsers,
        activeSubscriptions: snapshot.activeSubscriptions,
        totalRevenue: snapshot.totalRevenue,
        failureRate: snapshot.failureRate,
        completionRate: snapshot.completionRate,
      });
    } catch (error) {
      logger.error("Daily report generation error:", error);
    }
  }

  private async collectUserStats(): Promise<{ totalUsers: number }> {
    try {
      const response = await axios.get(
        `${this.authServiceUrl}/api/users/stats`,
        {
          timeout: 5000,
        }
      );

      return {
        totalUsers: response.data.data?.totalUsers || 0,
      };
    } catch (error) {
      logger.error("Failed to collect user stats:", error);
      return { totalUsers: 0 };
    }
  }

  private async collectCourseStats(): Promise<{
    popularCourses: PopularCourse[];
    completionRate: number;
    tutorStats: TutorStats[];
  }> {
    try {
      const response = await axios.get(
        `${this.courseServiceUrl}/api/courses/stats`,
        {
          timeout: 5000,
        }
      );

      return {
        popularCourses: response.data.data?.popularCourses || [],
        completionRate: response.data.data?.completionRate || 0,
        tutorStats: response.data.data?.tutorStats || [],
      };
    } catch (error) {
      logger.error("Failed to collect course stats:", error);
      return {
        popularCourses: [],
        completionRate: 0,
        tutorStats: [],
      };
    }
  }

  private async collectPaymentStats(): Promise<{
    activeSubscriptions: number;
    totalRevenue: number;
    failureRate: number;
  }> {
    try {
      const response = await axios.get(
        `${this.paymentServiceUrl}/api/subscriptions/stats`,
        {
          timeout: 5000,
        }
      );

      return {
        activeSubscriptions: response.data.data?.activeSubscriptions || 0,
        totalRevenue: response.data.data?.totalRevenue || 0,
        failureRate: response.data.data?.failureRate || 0,
      };
    } catch (error) {
      logger.error("Failed to collect payment stats:", error);
      return {
        activeSubscriptions: 0,
        totalRevenue: 0,
        failureRate: 0,
      };
    }
  }

  async getLatestAnalytics(): Promise<Analytics | null> {
    try {
      const snapshot = await AnalyticsSnapshotModel.findOne().sort({
        date: -1,
      });

      if (!snapshot) {
        return null;
      }

      return {
        totalUsers: snapshot.totalUsers,
        activeSubscriptions: snapshot.activeSubscriptions,
        totalRevenue: snapshot.totalRevenue,
        popularCourses: snapshot.popularCourses,
        failureRate: snapshot.failureRate,
        completionRate: snapshot.completionRate,
        tutorStats: snapshot.tutorStats,
      };
    } catch (error) {
      logger.error("Get latest analytics error:", error);
      return null;
    }
  }

  async getAnalyticsByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<Analytics[]> {
    try {
      const snapshots = await AnalyticsSnapshotModel.find({
        date: { $gte: startDate, $lte: endDate },
      }).sort({ date: -1 });

      return snapshots.map((snapshot) => ({
        totalUsers: snapshot.totalUsers,
        activeSubscriptions: snapshot.activeSubscriptions,
        totalRevenue: snapshot.totalRevenue,
        popularCourses: snapshot.popularCourses,
        failureRate: snapshot.failureRate,
        completionRate: snapshot.completionRate,
        tutorStats: snapshot.tutorStats,
      }));
    } catch (error) {
      logger.error("Get analytics by date range error:", error);
      return [];
    }
  }
}
