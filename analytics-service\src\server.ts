import express from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import mongoose from "mongoose";
import cron from "node-cron";

import analyticsRoutes from "./routes/analytics";
import reportsRoutes from "./routes/reports";
import { errorHandler } from "./middleware/errorHandler";
import { logger } from "./utils/logger";
import { DataCollectionService } from "./services/DataCollectionService";

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || [
      "http://localhost:3000",
    ],
    credentials: true,
  })
);

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: "Too many requests from this IP, please try again later.",
});

app.use(limiter);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    service: "analytics-service",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Routes
app.use("/api/analytics", analyticsRoutes);
app.use("/api/reports", reportsRoutes);

// Error handling
app.use(errorHandler);

// Cron jobs
const dataCollectionService = new DataCollectionService();

// Collect analytics data every hour
cron.schedule("0 * * * *", async () => {
  logger.info("Running analytics data collection...");
  await dataCollectionService.collectAllData();
});

// Generate daily reports at midnight
cron.schedule("0 0 * * *", async () => {
  logger.info("Generating daily reports...");
  await dataCollectionService.generateDailyReports();
});

// MongoDB connection
mongoose
  .connect(
    process.env.MONGODB_URI || "mongodb://localhost:27017/time_course_analytics"
  )
  .then(() => {
    logger.info("Connected to MongoDB");
    app.listen(PORT, () => {
      logger.info(`Analytics Service running on port ${PORT}`);
    });
  })
  .catch((error) => {
    logger.error("MongoDB connection error:", error);
    process.exit(1);
  });

export default app;
