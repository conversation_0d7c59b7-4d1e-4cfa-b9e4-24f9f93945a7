import { Request, Response } from 'express';
import { CoachingSessionModel } from '../models/CoachingSession';
import { UserRole, CoachingSessionStatus } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import axios from 'axios';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class CoachingController {
  async createCoachingSession(req: AuthenticatedRequest, res: Response) {
    try {
      // Only tutors can create coaching sessions
      if (req.user?.role !== UserRole.TUTOR) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Only tutors can create coaching sessions')
        );
      }

      const { courseId, topic, date, meetingLink, maxStudents = 20 } = req.body;
      const tutorId = req.user.userId;

      const coachingSession = new CoachingSessionModel({
        tutorId,
        courseId,
        topic,
        date: new Date(date),
        meetingLink,
        maxStudents,
        enrolledStudents: [],
        status: CoachingSessionStatus.SCHEDULED
      });

      await coachingSession.save();

      logger.info(`Coaching session created by tutor ${tutorId}: ${topic}`);

      res.status(HTTP_STATUS.CREATED).json(
        createApiResponse(true, 'Coaching session created successfully', coachingSession)
      );

    } catch (error) {
      logger.error('Create coaching session error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to create coaching session', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getCoachingSessions(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, status } = req.query;
      const query: any = {};

      // Filter by course if specified
      if (courseId) {
        query.courseId = courseId;
      }

      // Filter by status if specified
      if (status) {
        query.status = status;
      }

      // Tutors can only see their own sessions
      if (req.user?.role === UserRole.TUTOR) {
        query.tutorId = req.user.userId;
      }

      const sessions = await CoachingSessionModel.find(query).sort({ date: 1 });

      res.json(createApiResponse(true, 'Coaching sessions retrieved successfully', sessions));

    } catch (error) {
      logger.error('Get coaching sessions error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve coaching sessions', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getAvailableCoachingSessions(req: AuthenticatedRequest, res: Response) {
    try {
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      // Get terminated subscriptions for this student
      const terminatedSubscriptions = await this.getTerminatedSubscriptions(studentId);
      
      if (terminatedSubscriptions.length === 0) {
        return res.json(createApiResponse(true, 'No coaching sessions available', []));
      }

      const courseIds = terminatedSubscriptions.map((sub: any) => sub.courseId);

      // Find available coaching sessions for terminated courses
      const availableSessions = await CoachingSessionModel.find({
        courseId: { $in: courseIds },
        status: CoachingSessionStatus.SCHEDULED,
        date: { $gte: new Date() },
        $expr: { $lt: [{ $size: '$enrolledStudents' }, '$maxStudents'] }
      }).sort({ date: 1 });

      res.json(createApiResponse(true, 'Available coaching sessions retrieved successfully', availableSessions));

    } catch (error) {
      logger.error('Get available coaching sessions error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve available coaching sessions', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async enrollInCoachingSession(req: AuthenticatedRequest, res: Response) {
    try {
      const { sessionId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const session = await CoachingSessionModel.findById(sessionId);

      if (!session) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Coaching session not found')
        );
      }

      // Check if session is full
      if (session.enrolledStudents.length >= session.maxStudents) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createApiResponse(false, 'Coaching session is full')
        );
      }

      // Check if student is already enrolled
      if (session.enrolledStudents.includes(studentId)) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          createApiResponse(false, 'Already enrolled in this session')
        );
      }

      // Verify student is eligible (has terminated subscription for this course)
      const terminatedSubscriptions = await this.getTerminatedSubscriptions(studentId);
      const isEligible = terminatedSubscriptions.some((sub: any) => sub.courseId === session.courseId);

      if (!isEligible) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Not eligible for this coaching session')
        );
      }

      // Enroll student
      session.enrolledStudents.push(studentId);
      await session.save();

      logger.info(`Student ${studentId} enrolled in coaching session ${sessionId}`);

      res.json(createApiResponse(true, 'Successfully enrolled in coaching session', session));

    } catch (error) {
      logger.error('Enroll in coaching session error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to enroll in coaching session', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async updateCoachingSession(req: AuthenticatedRequest, res: Response) {
    try {
      const { sessionId } = req.params;
      const updates = req.body;

      const session = await CoachingSessionModel.findById(sessionId);

      if (!session) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Coaching session not found')
        );
      }

      // Only the tutor who created the session or admin can update it
      if (req.user?.role !== UserRole.ADMIN && session.tutorId !== req.user?.userId) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Access denied')
        );
      }

      const updatedSession = await CoachingSessionModel.findByIdAndUpdate(
        sessionId,
        { ...updates, updatedAt: new Date() },
        { new: true }
      );

      logger.info(`Coaching session updated: ${sessionId}`);

      res.json(createApiResponse(true, 'Coaching session updated successfully', updatedSession));

    } catch (error) {
      logger.error('Update coaching session error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to update coaching session', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  private async getTerminatedSubscriptions(studentId: string): Promise<any[]> {
    try {
      const paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://payment-service:3003';
      
      const response = await axios.get(`${paymentServiceUrl}/api/subscriptions/terminated?studentId=${studentId}`, {
        timeout: 5000
      });

      return response.data.data || [];

    } catch (error) {
      logger.error('Failed to get terminated subscriptions:', error);
      return [];
    }
  }
}