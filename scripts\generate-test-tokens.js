#!/usr/bin/env node

/**
 * Script untuk generate JWT tokens untuk testing
 * Jalankan dengan: node scripts/generate-test-tokens.js
 */

const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

// Define types directly to avoid path issues
const UserRole = {
  STUDENT: "student",
  TUTOR: "tutor",
  ADMIN: "admin",
};

const UserStatus = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  SUSPENDED: "suspended",
};

// MongoDB connection URI
const AUTH_DB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/time_course_auth";
const JWT_SECRET = process.env.JWT_SECRET || "fallback-secret";

// User schema
const userSchema = new mongoose.Schema(
  {
    uid: String,
    email: String,
    displayName: String,
    role: String,
    status: String,
  },
  { timestamps: true }
);

const UserModel = mongoose.model("User", userSchema);

async function generateTestTokens() {
  try {
    console.log("🔑 Generating JWT tokens for test users...");

    // Connect to database
    await mongoose.connect(AUTH_DB_URI);
    console.log("✅ Connected to auth database");

    // Get all test users
    const testUsers = await UserModel.find({ email: { $regex: "@test.com$" } });

    if (testUsers.length === 0) {
      console.log("❌ No test users found. Run seed-test-data.js first.");
      process.exit(1);
    }

    console.log("\n🎫 JWT Tokens for Testing:");
    console.log("=".repeat(100));

    const tokens = {};

    testUsers.forEach((user) => {
      const payload = {
        uid: user.uid,
        email: user.email,
        role: user.role,
        userId: user._id.toString(),
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: "7d" });
      tokens[user.role] = tokens[user.role] || [];
      tokens[user.role].push({
        email: user.email,
        token: token,
        userId: user._id.toString(),
      });

      console.log(`\n👤 ${user.role.toUpperCase()} - ${user.email}`);
      console.log(`   Token: ${token}`);
      console.log(`   User ID: ${user._id}`);
    });

    // Generate curl examples
    console.log("\n\n🌐 cURL Examples for Testing:");
    console.log("=".repeat(100));

    // Admin example
    if (tokens.admin && tokens.admin[0]) {
      console.log("\n📋 ADMIN - Get All Users:");
      console.log(`curl -X GET "http://localhost:3000/api/users" \\`);
      console.log(`  -H "Authorization: Bearer ${tokens.admin[0].token}" \\`);
      console.log(`  -H "Content-Type: application/json"`);
    }

    // Tutor example
    if (tokens.tutor && tokens.tutor[0]) {
      console.log("\n📚 TUTOR - Create Course:");
      console.log(`curl -X POST "http://localhost:3000/api/courses" \\`);
      console.log(`  -H "Authorization: Bearer ${tokens.tutor[0].token}" \\`);
      console.log(`  -H "Content-Type: application/json" \\`);
      console.log(`  -d '{
    "title": "Test Course",
    "description": "A test course",
    "category": "English",
    "duration": 30,
    "price": 100000,
    "currency": "IDR"
  }'`);
    }

    // Student example
    if (tokens.student && tokens.student[0]) {
      console.log("\n🎓 STUDENT - Get Profile:");
      console.log(`curl -X GET "http://localhost:3000/api/auth/profile" \\`);
      console.log(`  -H "Authorization: Bearer ${tokens.student[0].token}" \\`);
      console.log(`  -H "Content-Type: application/json"`);
    }

    // Save tokens to file for easy access
    const fs = require("fs");
    const tokensFile = "test-tokens.json";
    fs.writeFileSync(tokensFile, JSON.stringify(tokens, null, 2));
    console.log(`\n💾 Tokens saved to ${tokensFile}`);

    console.log("\n✅ Token generation completed!");
  } catch (error) {
    console.error("❌ Error generating tokens:", error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the token generation
if (require.main === module) {
  generateTestTokens();
}

module.exports = { generateTestTokens };
