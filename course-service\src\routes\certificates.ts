import { Router } from 'express';
import { CertificateController } from '../controllers/CertificateController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const certificateController = new CertificateController();

// Generate certificate for completed course
router.post('/generate/:courseId',
  authenticateToken,
  certificateController.generateCertificate
);

// Verify certificate by verification code (public endpoint)
router.get('/verify/:verificationCode',
  certificateController.verifyCertificate
);

// Get student's certificates
router.get('/student/me',
  authenticateToken,
  certificateController.getStudentCertificates
);

export default router;