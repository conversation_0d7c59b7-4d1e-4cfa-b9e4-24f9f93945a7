#!/usr/bin/env node

/**
 * Cross-platform development setup script
 * <PERSON><PERSON><PERSON> dengan: node scripts/dev-setup.js [command]
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const isWindows = process.platform === 'win32';

class DevSetup {
  constructor() {
    this.services = ['auth-service', 'course-service', 'payment-service', 'analytics-service', 'api-gateway'];
    this.processes = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[34m',    // Blue
      success: '\x1b[32m', // Green
      warning: '\x1b[33m', // Yellow
      error: '\x1b[31m',   // Red
      reset: '\x1b[0m'
    };

    const icons = {
      info: 'ℹ️ ',
      success: '✅',
      warning: '⚠️ ',
      error: '❌'
    };

    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
  }

  async execCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      exec(command, options, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve({ stdout, stderr });
        }
      });
    });
  }

  async checkDocker() {
    try {
      await this.execCommand('docker info');
      this.log('Docker is running', 'success');
      return true;
    } catch (error) {
      this.log('Docker is not running. Please start Docker first.', 'error');
      return false;
    }
  }

  async startDatabases() {
    this.log('Starting MongoDB databases...');
    
    try {
      await this.execCommand('docker-compose up -d mongo-auth mongo-course mongo-payment mongo-analytics');
      this.log('Databases started successfully', 'success');
      this.log('Waiting for databases to be ready...');
      await this.sleep(10000);
    } catch (error) {
      this.log('Failed to start databases', 'error');
      throw error;
    }
  }

  async installDependencies() {
    this.log('Checking and installing dependencies...');
    
    for (const service of this.services) {
      const nodeModulesPath = path.join(service, 'node_modules');
      if (!fs.existsSync(nodeModulesPath)) {
        this.log(`Installing ${service} dependencies...`);
        try {
          await this.execCommand('npm install', { cwd: service });
        } catch (error) {
          this.log(`Failed to install ${service} dependencies`, 'error');
          throw error;
        }
      }
    }
  }

  async startServices() {
    this.log('Starting services with hot reload...');
    
    // Create logs directory
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs');
    }

    // Start each service
    for (const service of this.services) {
      const logFile = path.join('logs', `${service}.log`);
      const logStream = fs.createWriteStream(logFile, { flags: 'w' });
      
      const child = spawn('npm', ['run', 'dev'], {
        cwd: service,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: isWindows
      });

      child.stdout.pipe(logStream);
      child.stderr.pipe(logStream);
      
      this.processes.push({
        name: service,
        process: child,
        pid: child.pid
      });

      this.log(`Started ${service} (PID: ${child.pid})`);
    }

    this.log('All services started in development mode', 'success');
    this.log('Services are running with hot reload enabled');
    this.log('Logs are available in logs/ directory');

    // Save PIDs for cleanup
    const pids = this.processes.map(p => p.pid).join(',');
    fs.writeFileSync('.dev-pids', pids);
  }

  async setupTestData() {
    this.log('Setting up test data...');
    
    // Wait for services to be ready
    await this.sleep(15000);
    
    try {
      // Create test users
      await this.execCommand('node scripts/seed-test-data.js');
      
      // Generate test tokens
      await this.execCommand('node scripts/generate-test-tokens.js');
      
      this.log('Test data setup completed', 'success');
    } catch (error) {
      this.log('Failed to setup test data', 'error');
      console.error(error);
    }
  }

  async stopServices() {
    this.log('Stopping development services...');
    
    // Kill processes from PID file
    if (fs.existsSync('.dev-pids')) {
      const pids = fs.readFileSync('.dev-pids', 'utf8').split(',');
      
      for (const pid of pids) {
        if (pid.trim()) {
          try {
            if (isWindows) {
              await this.execCommand(`taskkill /f /pid ${pid.trim()}`);
            } else {
              await this.execCommand(`kill ${pid.trim()}`);
            }
            this.log(`Stopped process ${pid.trim()}`);
          } catch (error) {
            // Process might already be dead
          }
        }
      }
      
      fs.unlinkSync('.dev-pids');
    }

    // Also kill all node processes (more aggressive)
    try {
      if (isWindows) {
        await this.execCommand('taskkill /f /im node.exe');
      } else {
        await this.execCommand('pkill -f "npm run dev"');
      }
    } catch (error) {
      // Ignore errors
    }

    this.log('Development services stopped', 'success');
  }

  async showStatus() {
    this.log('Service Status:');
    console.log('='.repeat(50));
    
    // Check databases
    console.log('📊 Databases:');
    try {
      const { stdout } = await this.execCommand('docker-compose ps mongo-auth mongo-course mongo-payment mongo-analytics');
      console.log(stdout);
    } catch (error) {
      console.log('  ❌ Failed to get database status');
    }
    
    console.log('\n🚀 Services:');
    if (fs.existsSync('.dev-pids')) {
      const pids = fs.readFileSync('.dev-pids', 'utf8').split(',');
      let runningCount = 0;
      
      for (const pid of pids) {
        if (pid.trim()) {
          try {
            if (isWindows) {
              await this.execCommand(`tasklist /fi "pid eq ${pid.trim()}" /fo csv`);
            } else {
              await this.execCommand(`kill -0 ${pid.trim()}`);
            }
            runningCount++;
          } catch (error) {
            // Process not running
          }
        }
      }
      
      console.log(`  ✅ Running services: ${runningCount}/${this.services.length}`);
    } else {
      console.log('  ❌ No development services running');
    }
    
    console.log('\n🌐 Endpoints:');
    console.log('  API Gateway:       http://localhost:3000');
    console.log('  Auth Service:      http://localhost:3001');
    console.log('  Course Service:    http://localhost:3002');
    console.log('  Payment Service:   http://localhost:3003');
    console.log('  Analytics Service: http://localhost:3004');
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async start() {
    if (!(await this.checkDocker())) return;
    
    await this.startDatabases();
    await this.installDependencies();
    await this.startServices();
    await this.setupTestData();
    await this.showStatus();
  }

  async stop() {
    await this.stopServices();
    try {
      await this.execCommand('docker-compose down');
      this.log('Databases stopped', 'success');
    } catch (error) {
      this.log('Failed to stop databases', 'error');
    }
  }

  async restart() {
    await this.stopServices();
    await this.startServices();
  }

  async dbOnly() {
    if (!(await this.checkDocker())) return;
    
    await this.startDatabases();
    this.log('Only databases started. Use "npm run dev" in each service directory for development.', 'success');
  }
}

// CLI interface
async function main() {
  const command = process.argv[2] || 'help';
  const devSetup = new DevSetup();
  
  try {
    switch (command.toLowerCase()) {
      case 'start':
        await devSetup.start();
        break;
      case 'stop':
        await devSetup.stop();
        break;
      case 'restart':
        await devSetup.restart();
        break;
      case 'status':
        await devSetup.showStatus();
        break;
      case 'db-only':
        await devSetup.dbOnly();
        break;
      case 'test-data':
        await devSetup.setupTestData();
        break;
      default:
        console.log('🔧 Development Setup Commands:');
        console.log('  start      - Start databases and all services in dev mode');
        console.log('  stop       - Stop all services and databases');
        console.log('  restart    - Restart development services only');
        console.log('  status     - Show status of all services');
        console.log('  db-only    - Start only databases');
        console.log('  test-data  - Setup test data only');
        console.log('');
        console.log('Examples:');
        console.log('  node scripts/dev-setup.js start');
        console.log('  node scripts/dev-setup.js status');
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = DevSetup;
