import { Request, Response } from 'express';
import { UserRole } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import { DataCollectionService } from '../services/DataCollectionService';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class ReportController {
  private dataCollectionService = new DataCollectionService();

  async generateUserReport(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can generate reports
      if (req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin access required')
        );
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'No analytics data available')
        );
      }

      const userReport = {
        totalUsers: analytics.totalUsers,
        activeSubscriptions: analytics.activeSubscriptions,
        userGrowthRate: 0, // Calculate based on historical data
        engagementMetrics: {
          completionRate: analytics.completionRate,
          failureRate: analytics.failureRate
        }
      };

      res.json(createApiResponse(true, 'User report generated successfully', userReport));

    } catch (error) {
      logger.error('Generate user report error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to generate user report', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async generateCourseReport(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins and tutors can generate course reports
      if (req.user?.role !== UserRole.ADMIN && req.user?.role !== UserRole.TUTOR) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin or tutor access required')
        );
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'No analytics data available')
        );
      }

      let courseReport = {
        popularCourses: analytics.popularCourses,
        completionRate: analytics.completionRate,
        totalRevenue: analytics.totalRevenue
      };

      // Filter for tutor's courses only
      if (req.user?.role === UserRole.TUTOR) {
        const tutorStats = analytics.tutorStats.find(stats => stats.tutorId === req.user?.userId);
        courseReport = {
          popularCourses: analytics.popularCourses.filter(course => 
            // This would need to be filtered based on tutor ownership
            true // Placeholder - implement proper filtering
          ),
          completionRate: analytics.completionRate,
          totalRevenue: tutorStats?.revenue || 0
        };
      }

      res.json(createApiResponse(true, 'Course report generated successfully', courseReport));

    } catch (error) {
      logger.error('Generate course report error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to generate course report', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async generateRevenueReport(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can generate revenue reports
      if (req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin access required')
        );
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'No analytics data available')
        );
      }

      const revenueReport = {
        totalRevenue: analytics.totalRevenue,
        revenueByTutor: analytics.tutorStats.map(tutor => ({
          tutorId: tutor.tutorId,
          name: tutor.name,
          revenue: tutor.revenue
        })),
        revenueGrowth: 0, // Calculate based on historical data
        averageRevenuePerStudent: analytics.totalUsers > 0 ? analytics.totalRevenue / analytics.totalUsers : 0
      };

      res.json(createApiResponse(true, 'Revenue report generated successfully', revenueReport));

    } catch (error) {
      logger.error('Generate revenue report error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to generate revenue report', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async generateTutorReport(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can generate tutor reports
      if (req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin access required')
        );
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'No analytics data available')
        );
      }

      const tutorReport = {
        totalTutors: analytics.tutorStats.length,
        tutorPerformance: analytics.tutorStats.sort((a, b) => b.revenue - a.revenue),
        averageStudentsPerTutor: analytics.tutorStats.length > 0 
          ? analytics.tutorStats.reduce((sum, tutor) => sum + tutor.totalStudents, 0) / analytics.tutorStats.length 
          : 0,
        topPerformingTutors: analytics.tutorStats
          .sort((a, b) => b.averageRating - a.averageRating)
          .slice(0, 5)
      };

      res.json(createApiResponse(true, 'Tutor report generated successfully', tutorReport));

    } catch (error) {
      logger.error('Generate tutor report error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to generate tutor report', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }
}