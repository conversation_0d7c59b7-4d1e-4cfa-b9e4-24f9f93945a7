import { Request, Response } from 'express';
import { UserModel } from '../models/User';
import { UserRole } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS, PAGINATION } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
  };
}

export class UserController {
  async getAllUsers(req: AuthenticatedRequest, res: Response) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin access required')
        );
      }

      const page = parseInt(req.query.page as string) || PAGINATION.DEFAULT_PAGE;
      const limit = Math.min(parseInt(req.query.limit as string) || PAGINATION.DEFAULT_LIMIT, PAGINATION.MAX_LIMIT);
      const role = req.query.role as string;
      const status = req.query.status as string;
      const search = req.query.search as string;

      // Build query
      const query: any = {};
      if (role) query.role = role;
      if (status) query.status = status;
      if (search) {
        query.$or = [
          { displayName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        UserModel.find(query)
          .select('-__v')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        UserModel.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json(createApiResponse(true, 'Users retrieved successfully', {
        data: users,
        pagination: {
          page,
          limit,
          total,
          pages: totalPages
        }
      }));

    } catch (error) {
      logger.error('Get all users error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve users', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getUserById(req: AuthenticatedRequest, res: Response) {
    try {
      const { userId } = req.params;

      // Users can only access their own data unless they're admin
      if (req.user?.role !== UserRole.ADMIN) {
        const requestingUser = await UserModel.findOne({ uid: req.user?.uid });
        if (!requestingUser || requestingUser._id.toString() !== userId) {
          return res.status(HTTP_STATUS.FORBIDDEN).json(
            createApiResponse(false, 'Access denied')
          );
        }
      }

      const user = await UserModel.findById(userId).select('-__v');

      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'User not found')
        );
      }

      res.json(createApiResponse(true, 'User retrieved successfully', user));

    } catch (error) {
      logger.error('Get user by ID error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve user', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async deleteUser(req: AuthenticatedRequest, res: Response) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          createApiResponse(false, 'Admin access required')
        );
      }

      const { userId } = req.params;

      const user = await UserModel.findByIdAndDelete(userId);

      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'User not found')
        );
      }

      logger.info(`User deleted by admin ${req.user?.email}: ${user.email}`);

      res.json(createApiResponse(true, 'User deleted successfully'));

    } catch (error) {
      logger.error('Delete user error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to delete user', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }
}