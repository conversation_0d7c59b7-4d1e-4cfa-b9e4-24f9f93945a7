import { Router } from 'express';
import { body } from 'express-validator';
import { PaymentController } from '../controllers/PaymentController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const paymentController = new PaymentController();

// Create payment
router.post('/',
  authenticateToken,
  body('courseId').notEmpty(),
  body('amount').isFloat({ min: 0 }),
  body('currency').optional().isIn(['IDR', 'USD']),
  validateRequest,
  paymentController.createPayment
);

// Get payment status
router.get('/:paymentId',
  authenticateToken,
  paymentController.getPaymentStatus
);

// Get student payments
router.get('/student/me',
  authenticateToken,
  paymentController.getStudentPayments
);

export default router;