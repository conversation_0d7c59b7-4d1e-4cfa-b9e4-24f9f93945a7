# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key

# Bunny.net Configuration
BUNNY_API_KEY=your-bunny-api-key
BUNNY_STORAGE_ZONE=your-storage-zone-name
BUNNY_CDN_HOSTNAME=your-cdn-hostname.b-cdn.net

# Xendit Configuration
XENDIT_SECRET_KEY=xnd_development_your-secret-key
XENDIT_WEBHOOK_TOKEN=your-webhook-verification-token

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com