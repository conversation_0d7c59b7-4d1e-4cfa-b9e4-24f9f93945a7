import { Router } from "express";
import { query } from "express-validator";
import { AnalyticsController } from "../controllers/AnalyticsController";
import { authenticateToken } from "../middleware/auth";
import { validateRequest } from "../middleware/validation";

const router = Router();
const analyticsController = new AnalyticsController();

// Get overview analytics
router.get(
  "/overview",
  authenticateToken,
  analyticsController.getOverviewAnalytics
);

// Get analytics by date range
router.get(
  "/range",
  authenticateToken,
  query("startDate").isISO8601(),
  query("endDate").isISO8601(),
  validateRequest,
  analyticsController.getAnalyticsByDateRange
);

// Get tutor analytics
router.get(
  "/tutor/:tutorId",
  authenticateToken,
  analyticsController.getTutorAnalytics
);

// Refresh analytics (manual trigger)
router.post(
  "/refresh",
  authenticateToken,
  analyticsController.refreshAnalytics
);

export default router;
