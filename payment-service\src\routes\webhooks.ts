import { Router } from 'express';
import { WebhookController } from '../controllers/WebhookController';

const router = Router();
const webhookController = new WebhookController();

// Xendit payment webhook
router.post('/xendit/payment',
  webhookController.handleXenditPayment
);

// Xendit invoice webhook
router.post('/xendit/invoice',
  webhookController.handleXenditInvoice
);

export default router;