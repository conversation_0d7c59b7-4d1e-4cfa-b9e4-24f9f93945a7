import mongoose, { Document, Schema } from "mongoose";
import { Payment as IPayment, PaymentStatus } from "../../../shared/types";

export interface PaymentDocument extends Omit<IPayment, "_id">, Document {}

const paymentSchema = new Schema<PaymentDocument>(
  {
    studentId: { type: String, required: true, index: true },
    courseId: { type: String, required: true, index: true },
    amount: { type: Number, required: true },
    currency: { type: String, required: true, default: "IDR" },
    status: {
      type: String,
      enum: Object.values(PaymentStatus),
      default: PaymentStatus.PENDING,
    },
    xenditPaymentId: { type: String, sparse: true },
    xenditInvoiceId: { type: String, sparse: true },
    paymentMethod: { type: String, default: "" },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Indexes
paymentSchema.index({ studentId: 1, status: 1 });
paymentSchema.index({ xenditInvoiceId: 1 }, { sparse: true });

export const PaymentModel = mongoose.model<PaymentDocument>(
  "Payment",
  paymentSchema
);
