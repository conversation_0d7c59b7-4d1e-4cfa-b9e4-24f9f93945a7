import { Router } from 'express';
import { query } from 'express-validator';
import { UserController } from '../controllers/UserController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const userController = new UserController();

// Get all users (admin only)
router.get('/',
  authenticateToken,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isIn(['student', 'tutor', 'admin']),
  query('status').optional().isIn(['active', 'inactive', 'suspended']),
  query('search').optional().trim(),
  validateRequest,
  userController.getAllUsers
);

// Get user by ID
router.get('/:userId',
  authenticateToken,
  userController.getUserById
);

// Delete user (admin only)
router.delete('/:userId',
  authenticateToken,
  userController.deleteUser
);

export default router;