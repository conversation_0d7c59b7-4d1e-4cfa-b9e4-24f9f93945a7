name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies for auth-service
      run: |
        cd auth-service
        npm ci

    - name: Install dependencies for course-service
      run: |
        cd course-service
        npm ci

    - name: Install dependencies for payment-service
      run: |
        cd payment-service
        npm ci

    - name: Install dependencies for analytics-service
      run: |
        cd analytics-service
        npm ci

    - name: Run TypeScript compilation
      run: |
        cd auth-service && npm run build
        cd ../course-service && npm run build
        cd ../payment-service && npm run build
        cd ../analytics-service && npm run build

    - name: Run tests
      run: |
        cd auth-service && npm test
        cd ../course-service && npm test
        cd ../payment-service && npm test
        cd ../analytics-service && npm test
      env:
        NODE_ENV: test
        MONGODB_URI: mongodb://localhost:27017/test

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push auth-service
      uses: docker/build-push-action@v4
      with:
        context: ./auth-service
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/time-course-auth:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push course-service
      uses: docker/build-push-action@v4
      with:
        context: ./course-service
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/time-course-course:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push payment-service
      uses: docker/build-push-action@v4
      with:
        context: ./payment-service
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/time-course-payment:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push analytics-service
      uses: docker/build-push-action@v4
      with:
        context: ./analytics-service
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/time-course-analytics:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to production
      run: |
        echo "Deployment step - configure based on your hosting provider"
        echo "Examples: AWS ECS, Google Cloud Run, Kubernetes, etc."