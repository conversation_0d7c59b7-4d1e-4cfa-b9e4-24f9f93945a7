{"name": "time-course-course-service", "version": "1.0.0", "description": "Course and Learning Management Service", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"axios": "^1.5.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.2.1", "form-data": "^4.0.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "winston": "^3.10.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}