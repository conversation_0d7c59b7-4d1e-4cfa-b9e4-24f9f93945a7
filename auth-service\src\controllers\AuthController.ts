import { Request, Response } from "express";
import { getAuth } from "firebase-admin/auth";
import jwt from "jsonwebtoken";
import { UserModel } from "../models/User";
import { UserRole, UserStatus } from "../../../shared/types";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS } from "../../../shared/utils/constants";
import { logger } from "../utils/logger";

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
  };
}

export class AuthController {
  async firebaseLogin(req: Request, res: Response) {
    try {
      const { idToken } = req.body;

      // Verify Firebase ID token
      const decodedToken = await getAuth().verifyIdToken(idToken);
      const { uid, email, name, picture } = decodedToken;

      if (!email) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(createApiResponse(false, "Email is required"));
      }

      // Check if user exists
      let user = await UserModel.findOne({ uid });

      if (!user) {
        // Create new user
        user = new UserModel({
          uid,
          email,
          displayName: name || email.split("@")[0],
          photoURL: picture,
          role: UserRole.STUDENT,
          status: UserStatus.ACTIVE,
        });

        await user.save();
        logger.info(`New user created: ${email}`);
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          uid: user.uid,
          email: user.email,
          role: user.role,
          userId: user._id,
        },
        process.env.JWT_SECRET || "fallback-secret",
        { expiresIn: "7d" }
      );

      res.json(
        createApiResponse(true, "Login successful", {
          user: {
            _id: user._id,
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            role: user.role,
            status: user.status,
          },
          token,
        })
      );
    } catch (error) {
      logger.error("Firebase login error:", error);
      res
        .status(HTTP_STATUS.UNAUTHORIZED)
        .json(
          createApiResponse(
            false,
            "Invalid token",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const user = await UserModel.findOne({ uid: req.user?.uid });

      if (!user) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "User not found"));
      }

      res.json(
        createApiResponse(true, "Profile retrieved successfully", {
          _id: user._id,
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          role: user.role,
          status: user.status,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        })
      );
    } catch (error) {
      logger.error("Get profile error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve profile",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async updateProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const { displayName, photoURL } = req.body;

      const user = await UserModel.findOneAndUpdate(
        { uid: req.user?.uid },
        {
          displayName,
          photoURL,
          updatedAt: new Date(),
        },
        { new: true }
      );

      if (!user) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "User not found"));
      }

      res.json(createApiResponse(true, "Profile updated successfully", user));
    } catch (error) {
      logger.error("Update profile error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to update profile",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async createTutor(req: AuthenticatedRequest, res: Response) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Admin access required"));
      }

      const { email, displayName } = req.body;

      // Check if user already exists
      const existingUser = await UserModel.findOne({ email });
      if (existingUser) {
        return res
          .status(HTTP_STATUS.CONFLICT)
          .json(
            createApiResponse(false, "User with this email already exists")
          );
      }

      // Create Firebase user
      const userRecord = await getAuth().createUser({
        email,
        displayName,
        emailVerified: true,
      });

      // Create tutor in database
      const tutor = new UserModel({
        uid: userRecord.uid,
        email,
        displayName,
        role: UserRole.TUTOR,
        status: UserStatus.ACTIVE,
      });

      await tutor.save();

      logger.info(`Tutor created by admin ${req.user?.email}: ${email}`);

      res.status(HTTP_STATUS.CREATED).json(
        createApiResponse(true, "Tutor account created successfully", {
          _id: tutor._id,
          uid: tutor.uid,
          email: tutor.email,
          displayName: tutor.displayName,
          role: tutor.role,
          status: tutor.status,
        })
      );
    } catch (error) {
      logger.error("Create tutor error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to create tutor account",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async updateUserStatus(req: AuthenticatedRequest, res: Response) {
    try {
      // Check if user is admin
      if (req.user?.role !== UserRole.ADMIN) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Admin access required"));
      }

      const { userId } = req.params;
      const { status } = req.body;

      const user = await UserModel.findByIdAndUpdate(
        userId,
        { status, updatedAt: new Date() },
        { new: true }
      );

      if (!user) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "User not found"));
      }

      logger.info(
        `User status updated by admin ${req.user?.email}: ${user.email} -> ${status}`
      );

      res.json(
        createApiResponse(true, "User status updated successfully", user)
      );
    } catch (error) {
      logger.error("Update user status error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to update user status",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async logout(req: AuthenticatedRequest, res: Response) {
    try {
      // In a production environment, you might want to blacklist the token
      // For now, we'll just return success
      res.json(createApiResponse(true, "Logged out successfully"));
    } catch (error) {
      logger.error("Logout error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to logout",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }
}
