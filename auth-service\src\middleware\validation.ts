import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS } from "../../../shared/utils/constants";

export const validateRequest = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createApiResponse(
        false,
        "Validation failed",
        undefined,
        errors
          .array()
          .map((err) => err.msg)
          .join(", ")
      )
    );
  }

  next();
};
