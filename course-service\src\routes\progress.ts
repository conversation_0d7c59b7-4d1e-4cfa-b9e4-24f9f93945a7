import { Router } from 'express';
import { ProgressController } from '../controllers/ProgressController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const progressController = new ProgressController();

// Get student progress for all courses
router.get('/student',
  authenticateToken,
  progressController.getStudentProgress
);

// Get detailed progress for specific course
router.get('/course/:courseId',
  authenticateToken,
  progressController.getCourseProgress
);

export default router;