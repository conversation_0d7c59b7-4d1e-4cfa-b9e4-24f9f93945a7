import { Request, Response } from 'express';
import { PaymentModel } from '../models/Payment';
import { SubscriptionModel } from '../models/Subscription';
import { PaymentStatus, SubscriptionStatus } from '../../../shared/types';
import { createApiResponse } from '../../../shared/utils/helpers';
import { HTTP_STATUS, COURSE_CONSTANTS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import { XenditService } from '../services/XenditService';

export class WebhookController {
  private xenditService = new XenditService();

  async handleXenditPayment(req: Request, res: Response) {
    try {
      const signature = req.headers['x-callback-token'] as string;
      const rawBody = req.body.toString();

      // Verify webhook signature
      if (!this.xenditService.verifyWebhookSignature(rawBody, signature)) {
        logger.warn('Invalid Xendit webhook signature');
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Invalid signature')
        );
      }

      const webhookData = JSON.parse(rawBody);
      logger.info('Xendit payment webhook received:', webhookData);

      // Process payment webhook
      await this.processPaymentWebhook(webhookData);

      res.json(createApiResponse(true, 'Webhook processed successfully'));

    } catch (error) {
      logger.error('Xendit payment webhook error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Webhook processing failed')
      );
    }
  }

  async handleXenditInvoice(req: Request, res: Response) {
    try {
      const signature = req.headers['x-callback-token'] as string;
      const rawBody = req.body.toString();

      // Verify webhook signature
      if (!this.xenditService.verifyWebhookSignature(rawBody, signature)) {
        logger.warn('Invalid Xendit webhook signature');
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Invalid signature')
        );
      }

      const webhookData = JSON.parse(rawBody);
      logger.info('Xendit invoice webhook received:', webhookData);

      // Process invoice webhook
      await this.processInvoiceWebhook(webhookData);

      res.json(createApiResponse(true, 'Webhook processed successfully'));

    } catch (error) {
      logger.error('Xendit invoice webhook error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Webhook processing failed')
      );
    }
  }

  private async processPaymentWebhook(webhookData: any): Promise<void> {
    try {
      const { external_id, status, payment_method } = webhookData;

      const payment = await PaymentModel.findById(external_id);

      if (!payment) {
        logger.warn(`Payment not found for external_id: ${external_id}`);
        return;
      }

      // Update payment status
      switch (status) {
        case 'PAID':
          payment.status = PaymentStatus.PAID;
          payment.paymentMethod = payment_method;
          await payment.save();

          // Auto-create subscription
          await this.createSubscriptionFromPayment(payment);
          break;

        case 'FAILED':
          payment.status = PaymentStatus.FAILED;
          await payment.save();
          break;

        case 'EXPIRED':
          payment.status = PaymentStatus.EXPIRED;
          await payment.save();
          break;

        default:
          logger.warn(`Unknown payment status: ${status}`);
      }

    } catch (error) {
      logger.error('Process payment webhook error:', error);
      throw error;
    }
  }

  private async processInvoiceWebhook(webhookData: any): Promise<void> {
    try {
      const { external_id, status, id: invoiceId } = webhookData;

      const payment = await PaymentModel.findById(external_id);

      if (!payment) {
        logger.warn(`Payment not found for external_id: ${external_id}`);
        return;
      }

      // Update payment with invoice details
      if (!payment.xenditInvoiceId) {
        payment.xenditInvoiceId = invoiceId;
      }

      // Update status based on invoice status
      switch (status) {
        case 'PAID':
          payment.status = PaymentStatus.PAID;
          await payment.save();

          // Auto-create subscription
          await this.createSubscriptionFromPayment(payment);
          break;

        case 'EXPIRED':
          payment.status = PaymentStatus.EXPIRED;
          await payment.save();
          break;

        case 'CANCELLED':
          payment.status = PaymentStatus.CANCELLED;
          await payment.save();
          break;

        default:
          logger.info(`Invoice status update: ${status} for ${external_id}`);
      }

    } catch (error) {
      logger.error('Process invoice webhook error:', error);
      throw error;
    }
  }

  private async createSubscriptionFromPayment(payment: any): Promise<void> {
    try {
      // Check if subscription already exists
      const existingSubscription = await SubscriptionModel.findOne({
        studentId: payment.studentId,
        courseId: payment.courseId
      });

      if (existingSubscription) {
        logger.info(`Subscription already exists for student ${payment.studentId}, course ${payment.courseId}`);
        return;
      }

      // Create subscription with default duration
      const startDate = new Date();
      const endDate = new Date(startDate.getTime() + (COURSE_CONSTANTS.DEFAULT_DURATION * 24 * 60 * 60 * 1000));

      const subscription = new SubscriptionModel({
        studentId: payment.studentId,
        courseId: payment.courseId,
        status: SubscriptionStatus.ACTIVE,
        startDate,
        endDate,
        failureCount: 0,
        maxFailures: COURSE_CONSTANTS.MAX_FAILURES,
        paymentId: payment._id,
        progress: {
          completedModules: [],
          completedLessons: [],
          completedAssignments: [],
          currentModule: 0,
          currentLesson: 0,
          completionPercentage: 0
        }
      });

      await subscription.save();

      logger.info(`Auto-created subscription for student ${payment.studentId}, course ${payment.courseId}`);

    } catch (error) {
      logger.error('Create subscription from payment error:', error);
      throw error;
    }
  }
}