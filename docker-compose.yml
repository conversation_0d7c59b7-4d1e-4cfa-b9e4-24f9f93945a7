services:
  # Auth & User Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo-auth:27017/time_course_auth
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongo-auth
    volumes:
      - ./auth-service:/app
      - /app/node_modules
    networks:
      - time-course-network

  # Course & Learning Service
  course-service:
    build:
      context: ./course-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongo-course:27017/time_course_courses
      - AUTH_SERVICE_URL=http://auth-service:3001
      - BUNNY_API_KEY=${BUNNY_API_KEY}
      - BUNNY_STORAGE_ZONE=${BUNNY_STORAGE_ZONE}
      - BUNNY_CDN_HOSTNAME=${BUNNY_CDN_HOSTNAME}
    depends_on:
      - mongo-course
    volumes:
      - ./course-service:/app
      - /app/node_modules
    networks:
      - time-course-network

  # Payment & Subscription Service
  payment-service:
    build:
      context: ./payment-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - MONGODB_URI=mongodb://mongo-payment:27017/time_course_payments
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - XENDIT_SECRET_KEY=${XENDIT_SECRET_KEY}
      - XENDIT_WEBHOOK_TOKEN=${XENDIT_WEBHOOK_TOKEN}
    depends_on:
      - mongo-payment
    volumes:
      - ./payment-service:/app
      - /app/node_modules
    networks:
      - time-course-network

  # Reporting & Analytics Service
  analytics-service:
    build:
      context: ./analytics-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - MONGODB_URI=mongodb://mongo-analytics:27017/time_course_analytics
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - PAYMENT_SERVICE_URL=http://payment-service:3003
    depends_on:
      - mongo-analytics
    volumes:
      - ./analytics-service:/app
      - /app/node_modules
    networks:
      - time-course-network

  # API Gateway (Optional but recommended)
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - PAYMENT_SERVICE_URL=http://payment-service:3003
      - ANALYTICS_SERVICE_URL=http://analytics-service:3004
    depends_on:
      - auth-service
      - course-service
      - payment-service
      - analytics-service
    volumes:
      - ./api-gateway:/app
      - /app/node_modules
    networks:
      - time-course-network

  # MongoDB instances for each service
  mongo-auth:
    image: mongo:6.0
    container_name: mongo-auth
    restart: always
    ports:
      - "27017:27017" # Expose for development
    volumes:
      - mongo-auth-data:/data/db
    networks:
      - time-course-network

  mongo-course:
    image: mongo:6.0
    container_name: mongo-course
    restart: always
    ports:
      - "27018:27017" # Different port to avoid conflicts
    volumes:
      - mongo-course-data:/data/db
    networks:
      - time-course-network

  mongo-payment:
    image: mongo:6.0
    container_name: mongo-payment
    restart: always
    ports:
      - "27019:27017" # Different port to avoid conflicts
    volumes:
      - mongo-payment-data:/data/db
    networks:
      - time-course-network

  mongo-analytics:
    image: mongo:6.0
    container_name: mongo-analytics
    restart: always
    ports:
      - "27020:27017" # Different port to avoid conflicts
    volumes:
      - mongo-analytics-data:/data/db
    networks:
      - time-course-network

volumes:
  mongo-auth-data:
  mongo-course-data:
  mongo-payment-data:
  mongo-analytics-data:

networks:
  time-course-network:
    driver: bridge
