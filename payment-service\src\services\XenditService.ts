import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../utils/logger';

interface CreateInvoiceRequest {
  externalId: string;
  amount: number;
  currency: string;
  description: string;
  customerEmail: string;
}

interface XenditInvoice {
  id: string;
  external_id: string;
  user_id: string;
  status: string;
  merchant_name: string;
  amount: number;
  currency: string;
  description: string;
  invoice_url: string;
  expiry_date: string;
  created: string;
  updated: string;
}

export class XenditService {
  private secretKey: string;
  private webhookToken: string;
  private baseUrl = 'https://api.xendit.co';

  constructor() {
    this.secretKey = process.env.XENDIT_SECRET_KEY || '';
    this.webhookToken = process.env.XENDIT_WEBHOOK_TOKEN || '';

    if (!this.secretKey || !this.webhookToken) {
      logger.warn('Xendit configuration incomplete. Payment features may not work properly.');
    }
  }

  async createInvoice(request: CreateInvoiceRequest): Promise<XenditInvoice> {
    try {
      const response = await axios.post(`${this.baseUrl}/v2/invoices`, {
        external_id: request.externalId,
        amount: request.amount,
        currency: request.currency,
        description: request.description,
        customer: {
          email: request.customerEmail
        },
        success_redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
        failure_redirect_url: `${process.env.FRONTEND_URL}/payment/failed`
      }, {
        headers: {
          'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`Xendit invoice created: ${request.externalId}`);
      return response.data;

    } catch (error) {
      logger.error('Xendit create invoice error:', error);
      throw new Error('Failed to create Xendit invoice');
    }
  }

  async getInvoice(invoiceId: string): Promise<XenditInvoice> {
    try {
      const response = await axios.get(`${this.baseUrl}/v2/invoices/${invoiceId}`, {
        headers: {
          'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`
        }
      });

      return response.data;

    } catch (error) {
      logger.error('Xendit get invoice error:', error);
      throw new Error('Failed to get Xendit invoice');
    }
  }

  verifyWebhookSignature(rawBody: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookToken)
        .update(rawBody)
        .digest('hex');

      return signature === expectedSignature;

    } catch (error) {
      logger.error('Webhook signature verification error:', error);
      return false;
    }
  }

  async expireInvoice(invoiceId: string): Promise<void> {
    try {
      await axios.post(`${this.baseUrl}/v2/invoices/${invoiceId}/expire`, {}, {
        headers: {
          'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`Xendit invoice expired: ${invoiceId}`);

    } catch (error) {
      logger.error('Xendit expire invoice error:', error);
      throw new Error('Failed to expire Xendit invoice');
    }
  }
}