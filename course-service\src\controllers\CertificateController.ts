import { Request, Response } from 'express';
import { CertificateModel } from '../models/Certificate';
import { CourseModel } from '../models/Course';
import { ProgressModel } from '../models/Progress';
import { UserRole } from '../../../shared/types';
import { createApiResponse, generateVerificationCode } from '../../../shared/utils/helpers';
import { HTTP_STATUS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import { CertificateService } from '../services/CertificateService';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class CertificateController {
  private certificateService = new CertificateService();

  async generateCertificate(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      // Check if certificate already exists
      const existingCertificate = await CertificateModel.findOne({ studentId, courseId });

      if (existingCertificate) {
        return res.json(createApiResponse(true, 'Certificate already exists', existingCertificate));
      }

      // Verify course completion
      const progress = await ProgressModel.findOne({ studentId, courseId });
      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Course not found')
        );
      }

      if (!progress || progress.completionPercentage < 100) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createApiResponse(false, 'Course must be 100% completed to generate certificate')
        );
      }

      // Generate certificate
      const verificationCode = generateVerificationCode();
      const certificateUrl = await this.certificateService.generateCertificatePDF({
        studentName: req.user?.email || 'Student', // In real app, get from user service
        courseName: course.title,
        completionDate: new Date(),
        verificationCode
      });

      const certificate = new CertificateModel({
        studentId,
        courseId,
        verificationCode,
        downloadUrl: certificateUrl,
        issuedAt: new Date()
      });

      await certificate.save();

      logger.info(`Certificate generated for student ${studentId}, course ${courseId}`);

      res.status(HTTP_STATUS.CREATED).json(
        createApiResponse(true, 'Certificate generated successfully', certificate)
      );

    } catch (error) {
      logger.error('Generate certificate error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to generate certificate', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async verifyCertificate(req: Request, res: Response) {
    try {
      const { verificationCode } = req.params;

      const certificate = await CertificateModel.findOne({ verificationCode });

      if (!certificate) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Certificate not found')
        );
      }

      const course = await CourseModel.findById(certificate.courseId);

      res.json(createApiResponse(true, 'Certificate verified successfully', {
        certificate,
        course: course ? {
          title: course.title,
          category: course.category
        } : null
      }));

    } catch (error) {
      logger.error('Verify certificate error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to verify certificate', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getStudentCertificates(req: AuthenticatedRequest, res: Response) {
    try {
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const certificates = await CertificateModel.find({ studentId }).sort({ issuedAt: -1 });

      // Get course details for each certificate
      const certificatesWithCourses = await Promise.all(
        certificates.map(async (cert) => {
          const course = await CourseModel.findById(cert.courseId);
          return {
            ...cert.toJSON(),
            course: course ? {
              title: course.title,
              category: course.category
            } : null
          };
        })
      );

      res.json(createApiResponse(true, 'Student certificates retrieved successfully', certificatesWithCourses));

    } catch (error) {
      logger.error('Get student certificates error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve student certificates', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }
}