import { Request, Response } from "express";
import { UserRole } from "../../../shared/types";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS } from "../../../shared/utils/constants";
import { logger } from "../utils/logger";
import { DataCollectionService } from "../services/DataCollectionService";

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class AnalyticsController {
  private dataCollectionService = new DataCollectionService();

  async getOverviewAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can access analytics
      if (req.user?.role !== UserRole.ADMIN) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Admin access required"));
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "No analytics data available"));
      }

      res.json(
        createApiResponse(true, "Analytics retrieved successfully", analytics)
      );
    } catch (error) {
      logger.error("Get overview analytics error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve analytics",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getAnalyticsByDateRange(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can access analytics
      if (req.user?.role !== UserRole.ADMIN) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Admin access required"));
      }

      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(
            createApiResponse(false, "Start date and end date are required")
          );
      }

      const start = new Date(startDate as string);
      const end = new Date(endDate as string);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(createApiResponse(false, "Invalid date format"));
      }

      const analytics =
        await this.dataCollectionService.getAnalyticsByDateRange(start, end);

      res.json(
        createApiResponse(true, "Analytics retrieved successfully", analytics)
      );
    } catch (error) {
      logger.error("Get analytics by date range error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve analytics",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getTutorAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      const { tutorId } = req.params;

      // Tutors can only access their own analytics, admins can access any
      if (req.user?.role === UserRole.TUTOR && req.user.userId !== tutorId) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      if (
        req.user?.role !== UserRole.TUTOR &&
        req.user?.role !== UserRole.ADMIN
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Tutor or admin access required"));
      }

      const analytics = await this.dataCollectionService.getLatestAnalytics();

      if (!analytics) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "No analytics data available"));
      }

      const tutorStats = analytics.tutorStats.find(
        (stats) => stats.tutorId === tutorId
      );

      if (!tutorStats) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Tutor analytics not found"));
      }

      res.json(
        createApiResponse(
          true,
          "Tutor analytics retrieved successfully",
          tutorStats
        )
      );
    } catch (error) {
      logger.error("Get tutor analytics error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve tutor analytics",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async refreshAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      // Only admins can refresh analytics
      if (req.user?.role !== UserRole.ADMIN) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Admin access required"));
      }

      await this.dataCollectionService.collectAllData();

      res.json(createApiResponse(true, "Analytics refreshed successfully"));
    } catch (error) {
      logger.error("Refresh analytics error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to refresh analytics",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }
}
