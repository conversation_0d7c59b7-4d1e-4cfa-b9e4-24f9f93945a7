import mongoose, { Document, Schema } from "mongoose";
import { Analytics, PopularCourse, TutorStats } from "../../../shared/types";

export interface AnalyticsSnapshotDocument
  extends Omit<Analytics, "_id">,
    Document {
  date: Date;
}

const popularCourseSchema = new Schema(
  {
    courseId: { type: String, required: true },
    title: { type: String, required: true },
    enrollments: { type: Number, required: true },
    revenue: { type: Number, required: true },
  },
  { _id: false }
);

const tutorStatsSchema = new Schema(
  {
    tutorId: { type: String, required: true },
    name: { type: String, required: true },
    totalStudents: { type: Number, required: true },
    totalCourses: { type: Number, required: true },
    averageRating: { type: Number, required: true },
    revenue: { type: Number, required: true },
  },
  { _id: false }
);

const analyticsSnapshotSchema = new Schema<AnalyticsSnapshotDocument>(
  {
    date: { type: Date, required: true, index: true },
    totalUsers: { type: Number, required: true },
    activeSubscriptions: { type: Number, required: true },
    totalRevenue: { type: Number, required: true },
    popularCourses: [popularCourseSchema],
    failureRate: { type: Number, required: true },
    completionRate: { type: Number, required: true },
    tutorStats: [tutorStatsSchema],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        ret._id = ret._id.toString();
        return ret;
      },
    },
  }
);

// Indexes
analyticsSnapshotSchema.index({ date: -1 });

export const AnalyticsSnapshotModel = mongoose.model<AnalyticsSnapshotDocument>(
  "AnalyticsSnapshot",
  analyticsSnapshotSchema
);
