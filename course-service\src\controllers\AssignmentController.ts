import { Request, Response } from 'express';
import { CourseModel } from '../models/Course';
import { AssignmentSubmissionModel } from '../models/AssignmentSubmission';
import { ProgressModel } from '../models/Progress';
import { UserRole, AnswerSubmission } from '../../../shared/types';
import { createApiResponse, calculateProgress } from '../../../shared/utils/helpers';
import { HTTP_STATUS, COURSE_CONSTANTS } from '../../../shared/utils/constants';
import { logger } from '../utils/logger';
import axios from 'axios';

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class AssignmentController {
  async submitAssignment(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, moduleId, lessonId, assignmentId } = req.params;
      const { answers }: { answers: AnswerSubmission[] } = req.body;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      // Check if assignment already submitted
      const existingSubmission = await AssignmentSubmissionModel.findOne({
        studentId,
        assignmentId
      });

      if (existingSubmission) {
        return res.status(HTTP_STATUS.CONFLICT).json(
          createApiResponse(false, 'Assignment already submitted')
        );
      }

      // Get course and assignment details
      const course = await CourseModel.findById(courseId);
      if (!course) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Course not found')
        );
      }

      const module = course.modules.id(moduleId);
      if (!module) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Module not found')
        );
      }

      const lesson = module.lessons.id(lessonId);
      if (!lesson) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Lesson not found')
        );
      }

      const assignment = lesson.assignments.id(assignmentId);
      if (!assignment) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Assignment not found')
        );
      }

      // Grade the assignment
      let totalScore = 0;
      let maxScore = 0;
      const gradedAnswers: AnswerSubmission[] = [];

      for (const answer of answers) {
        const question = assignment.questions.id(answer.questionId);
        if (!question) continue;

        maxScore += question.points;
        
        // Simple grading logic - exact match for now
        const isCorrect = answer.answer.trim().toLowerCase() === question.correctAnswer.trim().toLowerCase();
        const points = isCorrect ? question.points : 0;
        totalScore += points;

        gradedAnswers.push({
          questionId: answer.questionId,
          answer: answer.answer,
          isCorrect,
          points
        });
      }

      const scorePercentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
      const passed = scorePercentage >= COURSE_CONSTANTS.MIN_ASSIGNMENT_SCORE;

      // Create submission record
      const submission = new AssignmentSubmissionModel({
        studentId,
        assignmentId,
        lessonId,
        courseId,
        answers: gradedAnswers,
        score: totalScore,
        maxScore,
        passed,
        submittedAt: new Date()
      });

      await submission.save();

      // Update progress if passed
      if (passed) {
        await this.updateStudentProgress(studentId, courseId, assignmentId);
      } else {
        // Notify payment service about failure
        await this.notifyPaymentServiceOfFailure(studentId, courseId);
      }

      logger.info(`Assignment submitted by student ${studentId}: ${passed ? 'PASSED' : 'FAILED'} (${scorePercentage.toFixed(1)}%)`);

      res.json(createApiResponse(true, 'Assignment submitted successfully', {
        submission: {
          _id: submission._id,
          score: totalScore,
          maxScore,
          scorePercentage: scorePercentage.toFixed(1),
          passed,
          submittedAt: submission.submittedAt
        }
      }));

    } catch (error) {
      logger.error('Submit assignment error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to submit assignment', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  async getAssignmentResults(req: AuthenticatedRequest, res: Response) {
    try {
      const { assignmentId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          createApiResponse(false, 'Student ID required')
        );
      }

      const submission = await AssignmentSubmissionModel.findOne({
        studentId,
        assignmentId
      });

      if (!submission) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createApiResponse(false, 'Assignment submission not found')
        );
      }

      res.json(createApiResponse(true, 'Assignment results retrieved successfully', submission));

    } catch (error) {
      logger.error('Get assignment results error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createApiResponse(false, 'Failed to retrieve assignment results', undefined, error instanceof Error ? error.message : 'Unknown error')
      );
    }
  }

  private async updateStudentProgress(studentId: string, courseId: string, assignmentId: string) {
    try {
      let progress = await ProgressModel.findOne({ studentId, courseId });

      if (!progress) {
        progress = new ProgressModel({
          studentId,
          courseId,
          completedModules: [],
          completedLessons: [],
          completedAssignments: [],
          currentModule: 0,
          currentLesson: 0,
          completionPercentage: 0
        });
      }

      // Add assignment to completed list if not already there
      if (!progress.completedAssignments.includes(assignmentId)) {
        progress.completedAssignments.push(assignmentId);
      }

      // Get course to calculate progress
      const course = await CourseModel.findById(courseId);
      if (course) {
        const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
        const totalAssignments = course.modules.reduce((total, module) => 
          total + module.lessons.reduce((lessonTotal, lesson) => lessonTotal + lesson.assignments.length, 0), 0
        );

        progress.completionPercentage = calculateProgress(
          progress.completedModules,
          course.modules.length,
          progress.completedLessons,
          totalLessons
        );
      }

      await progress.save();
    } catch (error) {
      logger.error('Update student progress error:', error);
    }
  }

  private async notifyPaymentServiceOfFailure(studentId: string, courseId: string) {
    try {
      const paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://payment-service:3003';
      
      await axios.post(`${paymentServiceUrl}/api/subscriptions/failure`, {
        studentId,
        courseId
      }, {
        timeout: 5000
      });

    } catch (error) {
      logger.error('Failed to notify payment service of failure:', error);
      // Don't throw error as this is a background notification
    }
  }
}