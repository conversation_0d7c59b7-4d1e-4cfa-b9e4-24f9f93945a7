import { Router } from 'express';
import { body } from 'express-validator';
import { AssignmentController } from '../controllers/AssignmentController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const assignmentController = new AssignmentController();

// Submit assignment
router.post('/:courseId/modules/:moduleId/lessons/:lessonId/assignments/:assignmentId/submit',
  authenticateToken,
  body('answers').isArray({ min: 1 }),
  body('answers.*.questionId').notEmpty(),
  body('answers.*.answer').notEmpty(),
  validateRequest,
  assignmentController.submitAssignment
);

// Get assignment results
router.get('/assignments/:assignmentId/results',
  authenticateToken,
  assignmentController.getAssignmentResults
);

export default router;