import { Router } from 'express';
import { body, query } from 'express-validator';
import { SubscriptionController } from '../controllers/SubscriptionController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const subscriptionController = new SubscriptionController();

// Create subscription
router.post('/',
  authenticateToken,
  body('courseId').notEmpty(),
  body('paymentId').notEmpty(),
  body('duration').optional().isInt({ min: 1, max: 365 }),
  validateRequest,
  subscriptionController.createSubscription
);

// Get student subscriptions
router.get('/student/:studentId',
  authenticateToken,
  subscriptionController.getStudentSubscriptions
);

// Check subscription access
router.get('/access/:courseId',
  authenticateToken,
  subscriptionController.checkSubscriptionAccess
);

// Handle assignment failure (internal endpoint)
router.post('/failure',
  body('studentId').notEmpty(),
  body('courseId').notEmpty(),
  validateRequest,
  subscriptionController.handleAssignmentFailure
);

// Get terminated students (for coaching)
router.get('/terminated',
  authenticateToken,
  query('courseId').optional(),
  validateRequest,
  subscriptionController.getTerminatedStudents
);

export default router;