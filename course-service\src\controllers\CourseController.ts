import { Request, Response } from "express";
import { CourseModel } from "../models/Course";
import { ProgressModel } from "../models/Progress";
import { UserRole, CourseStatus } from "../../../shared/types";
import { createApiResponse } from "../../../shared/utils/helpers";
import { HTTP_STATUS, PAGINATION } from "../../../shared/utils/constants";
import { logger } from "../utils/logger";
import { BunnyService } from "../services/BunnyService";

interface AuthenticatedRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: UserRole;
    userId: string;
  };
}

export class CourseController {
  private bunnyService = new BunnyService();

  async createCourse(req: AuthenticatedRequest, res: Response) {
    try {
      // Only tutors and admins can create courses
      if (
        req.user?.role !== UserRole.TUTOR &&
        req.user?.role !== UserRole.ADMIN
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(
            createApiResponse(
              false,
              "Only tutors and admins can create courses"
            )
          );
      }

      const { title, description, category, duration, price, currency } =
        req.body;

      const course = new CourseModel({
        title,
        description,
        category,
        tutorId: req.user.userId,
        duration,
        price,
        currency: currency || "IDR",
        status: CourseStatus.DRAFT,
        modules: [],
      });

      await course.save();

      logger.info(`Course created by ${req.user.email}: ${title}`);

      res
        .status(HTTP_STATUS.CREATED)
        .json(createApiResponse(true, "Course created successfully", course));
    } catch (error) {
      logger.error("Create course error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to create course",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getAllCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) || PAGINATION.DEFAULT_PAGE;
      const limit = Math.min(
        parseInt(req.query.limit as string) || PAGINATION.DEFAULT_LIMIT,
        PAGINATION.MAX_LIMIT
      );
      const category = req.query.category as string;
      const search = req.query.search as string;
      const status = req.query.status as string;

      // Build query
      const query: any = {};

      // Students can only see published courses
      if (req.user?.role === UserRole.STUDENT) {
        query.status = CourseStatus.PUBLISHED;
      } else if (req.user?.role === UserRole.TUTOR) {
        // Tutors can see their own courses
        query.tutorId = req.user.userId;
      }

      if (category) query.category = category;
      if (status && req.user?.role !== UserRole.STUDENT) query.status = status;
      if (search) {
        query.$text = { $search: search };
      }

      const skip = (page - 1) * limit;

      const [courses, total] = await Promise.all([
        CourseModel.find(query)
          .select("-modules.lessons.assignments.questions.correctAnswer") // Hide correct answers
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        CourseModel.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json(
        createApiResponse(true, "Courses retrieved successfully", {
          data: courses,
          pagination: {
            page,
            limit,
            total,
            pages: totalPages,
          },
        })
      );
    } catch (error) {
      logger.error("Get all courses error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve courses",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getCourseById(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;

      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Course not found"));
      }

      // Check access permissions
      if (
        req.user?.role === UserRole.STUDENT &&
        course.status !== CourseStatus.PUBLISHED
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Course not available"));
      }

      if (
        req.user?.role === UserRole.TUTOR &&
        course.tutorId !== req.user.userId
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      // Hide correct answers for students
      let courseData = course.toJSON();
      if (req.user?.role === UserRole.STUDENT) {
        courseData = JSON.parse(JSON.stringify(courseData));
        courseData.modules.forEach((module: any) => {
          module.lessons.forEach((lesson: any) => {
            lesson.assignments.forEach((assignment: any) => {
              assignment.questions.forEach((question: any) => {
                delete question.correctAnswer;
              });
            });
          });
        });
      }

      res.json(
        createApiResponse(true, "Course retrieved successfully", courseData)
      );
    } catch (error) {
      logger.error("Get course by ID error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve course",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async updateCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;
      const updates = req.body;

      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Course not found"));
      }

      // Check permissions
      if (
        req.user?.role === UserRole.TUTOR &&
        course.tutorId !== req.user.userId
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      const updatedCourse = await CourseModel.findByIdAndUpdate(
        courseId,
        { ...updates, updatedAt: new Date() },
        { new: true }
      );

      logger.info(`Course updated by ${req.user?.email}: ${course.title}`);

      res.json(
        createApiResponse(true, "Course updated successfully", updatedCourse)
      );
    } catch (error) {
      logger.error("Update course error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to update course",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async deleteCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;

      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Course not found"));
      }

      // Check permissions
      if (
        req.user?.role === UserRole.TUTOR &&
        course.tutorId !== req.user.userId
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      await CourseModel.findByIdAndDelete(courseId);

      logger.info(`Course deleted by ${req.user?.email}: ${course.title}`);

      res.json(createApiResponse(true, "Course deleted successfully"));
    } catch (error) {
      logger.error("Delete course error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to delete course",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async publishCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;

      const course = await CourseModel.findById(courseId);

      if (!course) {
        return res
          .status(HTTP_STATUS.NOT_FOUND)
          .json(createApiResponse(false, "Course not found"));
      }

      // Check permissions
      if (
        req.user?.role === UserRole.TUTOR &&
        course.tutorId !== req.user.userId
      ) {
        return res
          .status(HTTP_STATUS.FORBIDDEN)
          .json(createApiResponse(false, "Access denied"));
      }

      // Validate course has content
      if (course.modules.length === 0) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(
            createApiResponse(
              false,
              "Course must have at least one module to publish"
            )
          );
      }

      course.status = CourseStatus.PUBLISHED;
      await course.save();

      logger.info(`Course published by ${req.user?.email}: ${course.title}`);

      res.json(
        createApiResponse(true, "Course published successfully", course)
      );
    } catch (error) {
      logger.error("Publish course error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to publish course",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }

  async getStudentProgress(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId } = req.params;
      const studentId = req.user?.userId;

      if (!studentId) {
        return res
          .status(HTTP_STATUS.UNAUTHORIZED)
          .json(createApiResponse(false, "Student ID required"));
      }

      const progress = await ProgressModel.findOne({ studentId, courseId });

      if (!progress) {
        // Create initial progress record
        const newProgress = new ProgressModel({
          studentId,
          courseId,
          completedModules: [],
          completedLessons: [],
          completedAssignments: [],
          currentModule: 0,
          currentLesson: 0,
          completionPercentage: 0,
        });

        await newProgress.save();
        return res.json(
          createApiResponse(true, "Progress initialized", newProgress)
        );
      }

      res.json(
        createApiResponse(true, "Progress retrieved successfully", progress)
      );
    } catch (error) {
      logger.error("Get student progress error:", error);
      res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(
          createApiResponse(
            false,
            "Failed to retrieve progress",
            undefined,
            error instanceof Error ? error.message : "Unknown error"
          )
        );
    }
  }
}
